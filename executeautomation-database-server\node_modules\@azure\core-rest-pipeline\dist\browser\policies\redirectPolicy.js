// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { redirectPolicyName as tspRedirectPolicyName, redirectPolicy as tspRedirectPolicy, } from "@typespec/ts-http-runtime/internal/policies";
/**
 * The programmatic identifier of the redirectPolicy.
 */
export const redirectPolicyName = tspRedirectPolicyName;
/**
 * A policy to follow Location headers from the server in order
 * to support server-side redirection.
 * In the browser, this policy is not used.
 * @param options - Options to control policy behavior.
 */
export function redirectPolicy(options = {}) {
    return tspRedirectPolicy(options);
}
//# sourceMappingURL=redirectPolicy.js.map