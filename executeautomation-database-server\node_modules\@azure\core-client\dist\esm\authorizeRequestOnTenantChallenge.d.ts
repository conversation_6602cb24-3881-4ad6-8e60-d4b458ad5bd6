import type { AuthorizeRequestOnChallengeOptions } from "@azure/core-rest-pipeline";
/**
 * Defines a callback to handle auth challenge for Storage APIs.
 * This implements the bearer challenge process described here: https://learn.microsoft.com/rest/api/storageservices/authorize-with-azure-active-directory#bearer-challenge
 * Handling has specific features for storage that departs to the general AAD challenge docs.
 **/
export declare const authorizeRequestOnTenantChallenge: (challengeOptions: AuthorizeRequestOnChallengeOptions) => Promise<boolean>;
//# sourceMappingURL=authorizeRequestOnTenantChallenge.d.ts.map