import { OperationOptions } from "@azure-rest/core-client";
/** Optional parameters. */
export interface GetKeyAttestationOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface GetRandomBytesOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface UpdateKeyRotationPolicyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface GetKeyRotationPolicyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface RecoverDeletedKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface PurgeDeletedKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface GetDeletedKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface GetDeletedKeysOptionalParams extends OperationOptions {
    /** Maximum number of results to return in a page. If not specified the service will return up to 25 results. */
    maxresults?: number;
}
/** Optional parameters. */
export interface ReleaseOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface UnwrapKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface WrapKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface VerifyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface SignOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface DecryptOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface EncryptOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface RestoreKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface BackupKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface GetKeysOptionalParams extends OperationOptions {
    /** Maximum number of results to return in a page. If not specified the service will return up to 25 results. */
    maxresults?: number;
}
/** Optional parameters. */
export interface GetKeyVersionsOptionalParams extends OperationOptions {
    /** Maximum number of results to return in a page. If not specified the service will return up to 25 results. */
    maxresults?: number;
}
/** Optional parameters. */
export interface GetKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface UpdateKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface DeleteKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface ImportKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface RotateKeyOptionalParams extends OperationOptions {
}
/** Optional parameters. */
export interface CreateKeyOptionalParams extends OperationOptions {
}
//# sourceMappingURL=options.d.ts.map