{"version": 3, "file": "pagingHelpers.js", "sourceRoot": "", "sources": ["../../../../src/generated/static-helpers/pagingHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAEL,eAAe,GAEhB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AAyFtD;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAMrC,MAAc,EACd,kBAAgD,EAChD,mBAAgE,EAChE,gBAA0B,EAC1B,UAA0C,EAAE;;IAE5C,MAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,QAAQ,mCAAI,OAAO,CAAC;IAC7C,MAAM,YAAY,GAAG,MAAA,OAAO,CAAC,YAAY,mCAAI,UAAU,CAAC;IACxD,MAAM,WAAW,GAAgD;QAC/D,OAAO,EAAE,KAAK,EAAE,QAAiB,EAAE,EAAE;YACnC,MAAM,MAAM,GACV,QAAQ,KAAK,SAAS;gBACpB,CAAC,CAAC,MAAM,kBAAkB,EAAE;gBAC5B,CAAC,CAAC,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;YACjD,kBAAkB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC7C,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,MAAmB,CAAC,CAAC;YAC/D,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACpD,MAAM,MAAM,GAAG,WAAW,CAAW,OAAO,EAAE,QAAQ,CAAU,CAAC;YACjE,OAAO;gBACL,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,QAAQ;aACvB,CAAC;QACJ,CAAC;QACD,MAAM,EAAE,CAAC,QAAwB,EAAE,EAAE;YACnC,MAAM,EAAE,iBAAiB,EAAE,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,CAAC;YAC7C,OAAO,oBAAoB,CAAC,WAAW,EAAE;gBACvC,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IACF,OAAO,qBAAqB,CAAC,WAAW,CAAC,CAAC;AAC5C,CAAC;AAED;;;;;;GAMG;AAEH,SAAS,qBAAqB,CAK5B,WAAwD;;IAExD,MAAM,IAAI,GAAG,oBAAoB,CAC/B,WAAW,CACZ,CAAC;IACF,OAAO;QACL,IAAI;YACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;QACrB,CAAC;QACD,CAAC,MAAM,CAAC,aAAa,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,EACJ,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,MAAM,mCACnB,CAAC,CAAC,QAAwB,EAAE,EAAE;YAC5B,MAAM,EAAE,iBAAiB,EAAE,GAAG,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,CAAC;YAC7C,OAAO,oBAAoB,CAAC,WAAW,EAAE;gBACvC,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC;KACL,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAKlC,WAAwD;;;QAExD,MAAM,KAAK,GAAG,oBAAoB,CAAC,WAAW,CAAC,CAAC;;YAChD,KAAyB,eAAA,UAAA,cAAA,KAAK,CAAA,WAAA,kFAAE,CAAC;gBAAR,qBAAK;gBAAL,WAAK;gBAAnB,MAAM,IAAI,KAAA,CAAA;gBACnB,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,IAA6B,CAAA,CAAA,CAAA,CAAC;YACvC,CAAC;;;;;;;;;IACH,CAAC;CAAA;AAED,SAAgB,oBAAoB;8EAKlC,WAAwD,EACxD,UAEI,EAAE;QAEN,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC7B,IAAI,QAAQ,GAAG,cAAM,WAAW,CAAC,OAAO,CACtC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,WAAW,CAAC,aAAa,CACtC,CAAA,CAAC;QACF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,6BAAO;QACT,CAAC;QACD,IAAI,MAAM,GAAG,QAAQ,CAAC,IAAwC,CAAC;QAC/D,MAAM,CAAC,iBAAiB,GAAG,QAAQ,CAAC,YAAY,CAAC;QACjD,oBAAM,MAAM,CAAA,CAAC;QACb,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC7B,QAAQ,GAAG,cAAM,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA,CAAC;YAC5D,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,6BAAO;YACT,CAAC;YACD,MAAM,GAAG,QAAQ,CAAC,IAAwC,CAAC;YAC3D,MAAM,CAAC,iBAAiB,GAAG,QAAQ,CAAC,YAAY,CAAC;YACjD,oBAAM,MAAM,CAAA,CAAC;QACf,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,IAAa,EAAE,YAAqB;IACvD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,QAAQ,GAAI,IAAgC,CAAC,YAAY,CAAC,CAAC;IAEjE,IACE,OAAO,QAAQ,KAAK,QAAQ;QAC5B,OAAO,QAAQ,KAAK,WAAW;QAC/B,QAAQ,KAAK,IAAI,EACjB,CAAC;QACD,MAAM,IAAI,SAAS,CACjB,iBAAiB,YAAY,oDAAoD,OAAO,QAAQ,EAAE,CACnG,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;QACtB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAc,IAAa,EAAE,QAAgB;IAC/D,MAAM,KAAK,GAAI,IAAgC,CAAC,QAAQ,CAAQ,CAAC;IACjE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,SAAS,CACjB,kFAAkF,QAAQ,EAAE,CAC7F,CAAC;IACJ,CAAC;IAED,OAAO,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,QAA+B,EAC/B,gBAA0B;IAE1B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAChD,MAAM,eAAe,CACnB,gDAAgD,QAAQ,CAAC,MAAM,EAAE,EACjE,QAAQ,CACT,CAAC;IACJ,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport {\n  Client,\n  createRestError,\n  PathUncheckedResponse,\n} from \"@azure-rest/core-client\";\nimport { RestError } from \"@azure/core-rest-pipeline\";\n\n/**\n * Options for the byPage method\n */\nexport interface PageSettings {\n  /**\n   * A reference to a specific page to start iterating from.\n   */\n  continuationToken?: string;\n}\n\n/**\n * An interface that describes a page of results.\n */\nexport type ContinuablePage<TElement, TPage = TElement[]> = TPage & {\n  /**\n   * The token that keeps track of where to continue the iterator\n   */\n  continuationToken?: string;\n};\n\n/**\n * An interface that allows async iterable iteration both to completion and by page.\n */\nexport interface PagedAsyncIterableIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings extends PageSettings = PageSettings,\n> {\n  /**\n   * The next method, part of the iteration protocol\n   */\n  next(): Promise<IteratorResult<TElement>>;\n  /**\n   * The connection to the async iterator, part of the iteration protocol\n   */\n  [Symbol.asyncIterator](): PagedAsyncIterableIterator<\n    TElement,\n    TPage,\n    TPageSettings\n  >;\n  /**\n   * Return an AsyncIterableIterator that works a page at a time\n   */\n  byPage: (\n    settings?: TPageSettings,\n  ) => AsyncIterableIterator<ContinuablePage<TElement, TPage>>;\n}\n\n/**\n * An interface that describes how to communicate with the service.\n */\nexport interface PagedResult<\n  TElement,\n  TPage = TElement[],\n  TPageSettings extends PageSettings = PageSettings,\n> {\n  /**\n   * Link to the first page of results.\n   */\n  firstPageLink?: string;\n  /**\n   * A method that returns a page of results.\n   */\n  getPage: (\n    pageLink?: string,\n  ) => Promise<{ page: TPage; nextPageLink?: string } | undefined>;\n  /**\n   * a function to implement the `byPage` method on the paged async iterator.\n   */\n  byPage?: (\n    settings?: TPageSettings,\n  ) => AsyncIterableIterator<ContinuablePage<TElement, TPage>>;\n\n  /**\n   * A function to extract elements from a page.\n   */\n  toElements?: (page: TPage) => TElement[];\n}\n\n/**\n * Options for the paging helper\n */\nexport interface BuildPagedAsyncIteratorOptions {\n  itemName?: string;\n  nextLinkName?: string;\n}\n\n/**\n * Helper to paginate results in a generic way and return a PagedAsyncIterableIterator\n */\nexport function buildPagedAsyncIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings extends PageSettings = PageSettings,\n  TResponse extends PathUncheckedResponse = PathUncheckedResponse,\n>(\n  client: Client,\n  getInitialResponse: () => PromiseLike<TResponse>,\n  processResponseBody: (result: TResponse) => PromiseLike<unknown>,\n  expectedStatuses: string[],\n  options: BuildPagedAsyncIteratorOptions = {},\n): PagedAsyncIterableIterator<TElement, TPage, TPageSettings> {\n  const itemName = options.itemName ?? \"value\";\n  const nextLinkName = options.nextLinkName ?? \"nextLink\";\n  const pagedResult: PagedResult<TElement, TPage, TPageSettings> = {\n    getPage: async (pageLink?: string) => {\n      const result =\n        pageLink === undefined\n          ? await getInitialResponse()\n          : await client.pathUnchecked(pageLink).get();\n      checkPagingRequest(result, expectedStatuses);\n      const results = await processResponseBody(result as TResponse);\n      const nextLink = getNextLink(results, nextLinkName);\n      const values = getElements<TElement>(results, itemName) as TPage;\n      return {\n        page: values,\n        nextPageLink: nextLink,\n      };\n    },\n    byPage: (settings?: TPageSettings) => {\n      const { continuationToken } = settings ?? {};\n      return getPageAsyncIterator(pagedResult, {\n        pageLink: continuationToken,\n      });\n    },\n  };\n  return getPagedAsyncIterator(pagedResult);\n}\n\n/**\n * returns an async iterator that iterates over results. It also has a `byPage`\n * method that returns pages of items at once.\n *\n * @param pagedResult - an object that specifies how to get pages.\n * @returns a paged async iterator that iterates over results.\n */\n\nfunction getPagedAsyncIterator<\n  TElement,\n  TPage = TElement[],\n  TPageSettings extends PageSettings = PageSettings,\n>(\n  pagedResult: PagedResult<TElement, TPage, TPageSettings>,\n): PagedAsyncIterableIterator<TElement, TPage, TPageSettings> {\n  const iter = getItemAsyncIterator<TElement, TPage, TPageSettings>(\n    pagedResult,\n  );\n  return {\n    next() {\n      return iter.next();\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n    byPage:\n      pagedResult?.byPage ??\n      ((settings?: TPageSettings) => {\n        const { continuationToken } = settings ?? {};\n        return getPageAsyncIterator(pagedResult, {\n          pageLink: continuationToken,\n        });\n      }),\n  };\n}\n\nasync function* getItemAsyncIterator<\n  TElement,\n  TPage,\n  TPageSettings extends PageSettings,\n>(\n  pagedResult: PagedResult<TElement, TPage, TPageSettings>,\n): AsyncIterableIterator<TElement> {\n  const pages = getPageAsyncIterator(pagedResult);\n  for await (const page of pages) {\n    yield* page as unknown as TElement[];\n  }\n}\n\nasync function* getPageAsyncIterator<\n  TElement,\n  TPage,\n  TPageSettings extends PageSettings,\n>(\n  pagedResult: PagedResult<TElement, TPage, TPageSettings>,\n  options: {\n    pageLink?: string;\n  } = {},\n): AsyncIterableIterator<ContinuablePage<TElement, TPage>> {\n  const { pageLink } = options;\n  let response = await pagedResult.getPage(\n    pageLink ?? pagedResult.firstPageLink,\n  );\n  if (!response) {\n    return;\n  }\n  let result = response.page as ContinuablePage<TElement, TPage>;\n  result.continuationToken = response.nextPageLink;\n  yield result;\n  while (response.nextPageLink) {\n    response = await pagedResult.getPage(response.nextPageLink);\n    if (!response) {\n      return;\n    }\n    result = response.page as ContinuablePage<TElement, TPage>;\n    result.continuationToken = response.nextPageLink;\n    yield result;\n  }\n}\n\n/**\n * Gets for the value of nextLink in the body\n */\nfunction getNextLink(body: unknown, nextLinkName?: string): string | undefined {\n  if (!nextLinkName) {\n    return undefined;\n  }\n\n  const nextLink = (body as Record<string, unknown>)[nextLinkName];\n\n  if (\n    typeof nextLink !== \"string\" &&\n    typeof nextLink !== \"undefined\" &&\n    nextLink !== null\n  ) {\n    throw new RestError(\n      `Body Property ${nextLinkName} should be a string or undefined or null but got ${typeof nextLink}`,\n    );\n  }\n\n  if (nextLink === null) {\n    return undefined;\n  }\n\n  return nextLink;\n}\n\n/**\n * Gets the elements of the current request in the body.\n */\nfunction getElements<T = unknown>(body: unknown, itemName: string): T[] {\n  const value = (body as Record<string, unknown>)[itemName] as T[];\n  if (!Array.isArray(value)) {\n    throw new RestError(\n      `Couldn't paginate response\\n Body doesn't contain an array property with name: ${itemName}`,\n    );\n  }\n\n  return value ?? [];\n}\n\n/**\n * Checks if a request failed\n */\nfunction checkPagingRequest(\n  response: PathUncheckedResponse,\n  expectedStatuses: string[],\n): void {\n  if (!expectedStatuses.includes(response.status)) {\n    throw createRestError(\n      `Pagination failed with unexpected statusCode ${response.status}`,\n      response,\n    );\n  }\n}\n"]}