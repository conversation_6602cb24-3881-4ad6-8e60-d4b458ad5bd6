# GitHub MCP Server 官方安装指南

## 📋 项目概述

**目标**: 删除非官方GitHub Project Manager工具，安装GitHub官方MCP Server  
**官方仓库**: `github/github-mcp-server`  
**非官方仓库**: `kunwarVivek/mcp-github-project-manager` (已删除)

## ✅ 已完成的清理工作

### 1. 备份原配置
- 备份文件位置: `C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment\augment-global-state\mcpServers.json.backup-20250108-154xxx`

### 2. 删除非官方工具
- 删除目录: `F:\MCP\github-project-manager`
- 从VS Code MCP配置中移除非官方版本

### 3. 更新MCP配置
- 保留了其他8个MCP工具的配置
- 清理了非官方GitHub Project Manager的配置项

## 🛠️ 官方安装方案

### 方案1: 预编译二进制文件 (推荐)

#### 下载地址
```
https://github.com/github/github-mcp-server/releases
```

#### Windows版本
```
github-mcp-server_windows_amd64.zip
```

#### 安装步骤
1. 下载并解压到: `F:\MCP\github-official\`
2. 确保文件名为: `github-mcp-server.exe`
3. 运行命令测试: `github-mcp-server.exe stdio`

### 方案2: 从源码编译

#### 前置要求
- Go 1.19+ 版本

#### 编译步骤
```bash
git clone https://github.com/github/github-mcp-server.git
cd github-mcp-server
go build -o github-mcp-server.exe ./cmd/github-mcp-server
```

### 方案3: 使用npx

#### 前置要求
- Node.js 环境

#### 安装命令
```bash
npx @github/github-mcp-server
```

## 🔧 VS Code配置

### 配置文件位置
```
C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment\augment-global-state\mcpServers.json
```

### 配置模板
在现有配置数组中添加以下配置项:

```json
{
  "type": "stdio",
  "name": "GitHub Official MCP Server",
  "command": "F:\\MCP\\github-official\\github-mcp-server.exe",
  "arguments": "stdio",
  "workingDirectory": "F:\\MCP\\github-official",
  "useShellInterpolation": true,
  "id": "github-official-mcp",
  "disabled": false,
  "timeout": 120,
  "env": {
    "GITHUB_PERSONAL_ACCESS_TOKEN": "你的GitHub令牌"
  }
}
```

## 🔑 GitHub Personal Access Token

### 创建步骤
1. 访问: https://github.com/settings/tokens
2. 点击 "Generate new token (classic)"
3. 设置过期时间和权限

### 必需权限
- `repo` - 访问仓库
- `project` - 项目管理
- `write:org` - 组织写入权限

### 配置方法
将生成的token填入配置文件的 `GITHUB_PERSONAL_ACCESS_TOKEN` 字段

## 📁 目录结构

```
F:\MCP\
├── github-official\          # 新建的官方版本目录
│   └── github-mcp-server.exe  # 下载的二进制文件
├── context7\                  # 保留
├── playwright\                # 保留
├── sequential-thinking\       # 保留
├── memory\                    # 保留
├── mcp-feedback-enhanced\     # 保留
├── ghidra\                    # 保留
├── markitdown\                # 保留
└── ssh\                       # 保留
```

## ⚡ 快速启动步骤

### 1. 下载安装
- 从GitHub官方仓库下载预编译二进制文件
- 解压到 `F:\MCP\github-official\` 目录

### 2. 配置Token
- 创建GitHub Personal Access Token
- 设置必需的权限

### 3. 更新配置
- 将配置模板添加到VS Code MCP配置文件
- 填入正确的token值

### 4. 重启测试
- 重启VS Code
- 测试GitHub MCP Server是否正常工作

## 🚨 注意事项

### 安全提醒
- GitHub Token具有敏感权限，请妥善保管
- 不要将token提交到代码仓库

### 性能优化
- 避免使用Docker方案（会导致系统卡顿）
- 推荐使用预编译二进制文件方案

### 兼容性
- 确保Windows系统架构为amd64
- 如遇到权限问题，可能需要以管理员身份运行

## 📞 技术支持

如果安装过程中遇到问题，可以：
1. 查看GitHub官方仓库的Issues
2. 检查VS Code的MCP扩展日志
3. 验证GitHub Token的权限设置

---

**文档创建时间**: 2025-01-08  
**最后更新**: 2025-01-08  
**版本**: v1.0
