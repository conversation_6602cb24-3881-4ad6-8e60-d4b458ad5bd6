{"version": 3, "file": "keyVaultClient.d.ts", "sourceRoot": "", "sources": ["../../../src/generated/keyVaultClient.ts"], "names": [], "mappings": "AAGA,OAAO,EAGL,4BAA4B,EAC7B,MAAM,gBAAgB,CAAC;AACxB,OAAO,EACL,mBAAmB,EACnB,SAAS,EACT,mBAAmB,EACnB,gBAAgB,EAChB,mBAAmB,EACnB,OAAO,EACP,eAAe,EACf,oBAAoB,EACpB,uBAAuB,EACvB,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,eAAe,EACf,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,WAAW,EACZ,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,+BAA+B,EAC/B,4BAA4B,EAC5B,qCAAqC,EACrC,kCAAkC,EAClC,+BAA+B,EAC/B,6BAA6B,EAC7B,2BAA2B,EAC3B,4BAA4B,EAC5B,qBAAqB,EACrB,uBAAuB,EACvB,qBAAqB,EACrB,oBAAoB,EACpB,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,wBAAwB,EACxB,uBAAuB,EACvB,qBAAqB,EACrB,4BAA4B,EAC5B,oBAAoB,EACpB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACxB,MAAM,kBAAkB,CAAC;AA4B1B,OAAO,EAAE,0BAA0B,EAAE,MAAM,mCAAmC,CAAC;AAC/E,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAEnD,OAAO,EAAE,4BAA4B,EAAE,MAAM,0BAA0B,CAAC;AAExE,qBAAa,cAAc;IACzB,OAAO,CAAC,OAAO,CAAkB;IACjC,wDAAwD;IACxD,SAAgB,QAAQ,EAAE,QAAQ,CAAC;IAEnC,qHAAqH;gBAEnH,aAAa,EAAE,MAAM,EACrB,UAAU,EAAE,eAAe,EAC3B,OAAO,GAAE,4BAAiC;IAa5C,0IAA0I;IAC1I,iBAAiB,CACf,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,OAAO,GAAE,+BAAwD,GAChE,OAAO,CAAC,SAAS,CAAC;IAIrB,qFAAqF;IACrF,cAAc,CACZ,UAAU,EAAE,qBAAqB,EACjC,OAAO,GAAE,4BAAqD,GAC7D,OAAO,CAAC,WAAW,CAAC;IAIvB,8HAA8H;IAC9H,uBAAuB,CACrB,OAAO,EAAE,MAAM,EACf,iBAAiB,EAAE,iBAAiB,EACpC,OAAO,GAAE,qCAA8D,GACtE,OAAO,CAAC,iBAAiB,CAAC;IAS7B,iKAAiK;IACjK,oBAAoB,CAClB,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,kCAA2D,GACnE,OAAO,CAAC,iBAAiB,CAAC;IAI7B,+WAA+W;IAC/W,iBAAiB,CACf,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,+BAAwD,GAChE,OAAO,CAAC,SAAS,CAAC;IAIrB,+PAA+P;IAC/P,eAAe,CACb,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,6BAAsD,GAC9D,OAAO,CAAC,IAAI,CAAC;IAIhB,2PAA2P;IAC3P,aAAa,CACX,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,2BAAoD,GAC5D,OAAO,CAAC,gBAAgB,CAAC;IAI5B,gbAAgb;IAChb,cAAc,CACZ,OAAO,GAAE,4BAAqD,GAC7D,0BAA0B,CAAC,cAAc,CAAC;IAI7C,+JAA+J;IAC/J,OAAO,CACL,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,oBAAoB,EAChC,OAAO,GAAE,qBAA8C,GACtD,OAAO,CAAC,gBAAgB,CAAC;IAI5B,yVAAyV;IACzV,SAAS,CACP,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,uBAAuB,EACnC,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,kBAAkB,CAAC;IAI9B,0hBAA0hB;IAC1hB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,uBAAuB,EACnC,OAAO,GAAE,qBAA8C,GACtD,OAAO,CAAC,kBAAkB,CAAC;IAI9B,8aAA8a;IAC9a,MAAM,CACJ,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,mBAAmB,EAC/B,OAAO,GAAE,oBAA6C,GACrD,OAAO,CAAC,eAAe,CAAC;IAI3B,8MAA8M;IAC9M,IAAI,CACF,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,iBAAiB,EAC7B,OAAO,GAAE,kBAA2C,GACnD,OAAO,CAAC,kBAAkB,CAAC;IAI9B,+uBAA+uB;IAC/uB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,uBAAuB,EACnC,OAAO,GAAE,qBAA8C,GACtD,OAAO,CAAC,kBAAkB,CAAC;IAI9B,sqBAAsqB;IACtqB,OAAO,CACL,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,uBAAuB,EACnC,OAAO,GAAE,qBAA8C,GACtD,OAAO,CAAC,kBAAkB,CAAC;IAI9B,45BAA45B;IAC55B,UAAU,CACR,UAAU,EAAE,oBAAoB,EAChC,OAAO,GAAE,wBAAiD,GACzD,OAAO,CAAC,SAAS,CAAC;IAIrB,o7BAAo7B;IACp7B,SAAS,CACP,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,eAAe,CAAC;IAI3B,wXAAwX;IACxX,OAAO,CACL,OAAO,GAAE,qBAA8C,GACtD,0BAA0B,CAAC,OAAO,CAAC;IAItC,oIAAoI;IACpI,cAAc,CACZ,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,4BAAqD,GAC7D,0BAA0B,CAAC,OAAO,CAAC;IAItC,kMAAkM;IAClM,MAAM,CACJ,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,OAAO,GAAE,oBAA6C,GACrD,OAAO,CAAC,SAAS,CAAC;IAIrB,+MAA+M;IAC/M,SAAS,CACP,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,UAAU,EAAE,mBAAmB,EAC/B,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,SAAS,CAAC;IAIrB,mTAAmT;IACnT,SAAS,CACP,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,gBAAgB,CAAC;IAI5B,kOAAkO;IAClO,SAAS,CACP,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,mBAAmB,EAC/B,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,SAAS,CAAC;IAIrB,yGAAyG;IACzG,SAAS,CACP,OAAO,EAAE,MAAM,EACf,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,SAAS,CAAC;IAIrB,iNAAiN;IACjN,SAAS,CACP,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,mBAAmB,EAC/B,OAAO,GAAE,uBAAgD,GACxD,OAAO,CAAC,SAAS,CAAC;CAGtB"}