{"version": 3, "file": "cryptographyClientModels.js", "sourceRoot": "", "sources": ["../../src/cryptographyClientModels.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EAGL,wBAAwB,IAAI,kBAAkB,EAC9C,iCAAiC,IAAI,wBAAwB,EAC7D,kCAAkC,IAAI,yBAAyB,EAE/D,mBAAmB,IAAI,aAAa,EACpC,2BAA2B,IAAI,iCAAiC,GACjE,MAAM,6BAA6B,CAAC;AAErC,OAAO,EAGL,yBAAyB,EACzB,kBAAkB,EAClB,iCAAiC,EACjC,aAAa,EACb,wBAAwB,GAEzB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CryptographyOptions, KeyVaultKey } from \"./keysModels.js\";\n\nimport type { <PERSON>sonWebKey } from \"./generated/models/index.js\";\nimport {\n  JsonWebKeyEncryptionAlgorithm as EncryptionAlgorithm,\n  JsonWebKeyCurveName as KeyCurveName,\n  KnownJsonWebKeyCurveName as KnownKeyCurveNames,\n  KnownJsonWebKeySignatureAlgorithm as KnownSignatureAlgorithms,\n  KnownJsonWebKeyEncryptionAlgorithm as KnownEncryptionAlgorithms,\n  JsonWebKeySignatureAlgorithm as SignatureAlgorithm,\n  KnownJsonWebKeyType as KnownKeyTypes,\n  KnownKeyEncryptionAlgorithm as KnownKeyExportEncryptionAlgorithm,\n} from \"./generated/models/index.js\";\n\nexport {\n  EncryptionAlgorithm,\n  KeyCurveName,\n  KnownEncryptionAlgorithms,\n  KnownKeyCurveNames,\n  KnownKeyExportEncryptionAlgorithm,\n  KnownKeyTypes,\n  KnownSignatureAlgorithms,\n  SignatureAlgorithm,\n};\n\n/**\n * Supported algorithms for key wrapping/unwrapping\n */\nexport type KeyWrapAlgorithm =\n  | \"A128KW\"\n  | \"A192KW\"\n  | \"A256KW\"\n  | \"RSA-OAEP\"\n  | \"RSA-OAEP-256\"\n  | \"RSA1_5\"\n  | \"CKM_AES_KEY_WRAP\"\n  | \"CKM_AES_KEY_WRAP_PAD\";\n\n/**\n * Result of the {@link encrypt} operation.\n */\nexport interface EncryptResult {\n  /**\n   * Result of the {@link encrypt} operation in bytes.\n   */\n  result: Uint8Array;\n  /**\n   * The {@link EncryptionAlgorithm} used to encrypt the data.\n   */\n  algorithm: EncryptionAlgorithm;\n  /**\n   * The ID of the Key Vault Key used to encrypt the data.\n   */\n  keyID?: string;\n  /**\n   * The initialization vector used for encryption.\n   */\n  iv?: Uint8Array;\n  /**\n   * The authentication tag resulting from encryption with a symmetric key including A128GCM, A192GCM, and A256GCM.\n   */\n  authenticationTag?: Uint8Array;\n  /**\n   * Additional data that is authenticated during decryption but not encrypted.\n   */\n  additionalAuthenticatedData?: Uint8Array;\n}\n\n/**\n * Result of the {@link wrap} operation.\n */\nexport interface WrapResult {\n  /**\n   * Result of the {@link wrap} operation in bytes.\n   */\n  result: Uint8Array;\n  /**\n   * The ID of the Key Vault Key used to wrap the data.\n   */\n  keyID?: string;\n  /**\n   * The {@link EncryptionAlgorithm} used to wrap the data.\n   */\n  algorithm: KeyWrapAlgorithm;\n}\n\n/**\n * Result of the {@link unwrap} operation.\n */\nexport interface UnwrapResult {\n  /**\n   * Result of the {@link unwrap} operation in bytes.\n   */\n  result: Uint8Array;\n  /**\n   * The ID of the Key Vault Key used to unwrap the data.\n   */\n  keyID?: string;\n  /**\n   * The {@link KeyWrapAlgorithm} used to unwrap the data.\n   */\n  algorithm: KeyWrapAlgorithm;\n}\n/**\n * Result of the {@link decrypt} operation.\n */\nexport interface DecryptResult {\n  /**\n   * Result of the {@link decrypt} operation in bytes.\n   */\n  result: Uint8Array;\n  /**\n   * The ID of the Key Vault Key used to decrypt the encrypted data.\n   */\n  keyID?: string;\n  /**\n   * The {@link EncryptionAlgorithm} used to decrypt the encrypted data.\n   */\n  algorithm: EncryptionAlgorithm;\n}\n\n/**\n * Result of the {@link sign} operation.\n */\nexport interface SignResult {\n  /**\n   * Result of the {@link sign} operation in bytes.\n   */\n  result: Uint8Array;\n  /**\n   * The ID of the Key Vault Key used to sign the data.\n   */\n  keyID?: string;\n  /**\n   * The {@link EncryptionAlgorithm} used to sign the data.\n   */\n  algorithm: SignatureAlgorithm;\n}\n\n/**\n * Result of the {@link verify} operation.\n */\nexport interface VerifyResult {\n  /**\n   * Result of the {@link verify} operation in bytes.\n   */\n  result: boolean;\n  /**\n   * The ID of the Key Vault Key used to verify the data.\n   */\n  keyID?: string;\n}\n\n/**\n * Options for {@link encrypt}.\n */\nexport interface EncryptOptions extends CryptographyOptions {}\n\n/**\n * Options for {@link decrypt}.\n */\nexport interface DecryptOptions extends CryptographyOptions {}\n\n/**\n * Options for {@link sign}.\n */\nexport interface SignOptions extends CryptographyOptions {}\n\n/**\n * Options for {@link verify}.\n */\nexport interface VerifyOptions extends CryptographyOptions {}\n\n/**\n * Options for {@link verifyData}\n */\nexport interface VerifyDataOptions extends CryptographyOptions {}\n\n/**\n * Options for {@link wrapKey}.\n */\nexport interface WrapKeyOptions extends CryptographyOptions {}\n\n/**\n * Options for {@link unwrapKey}.\n */\nexport interface UnwrapKeyOptions extends CryptographyOptions {}\n\n/**\n * A union type representing all supported RSA encryption algorithms.\n */\nexport type RsaEncryptionAlgorithm = \"RSA1_5\" | \"RSA-OAEP\" | \"RSA-OAEP-256\";\n\n/**\n * Encryption parameters for RSA encryption algorithms.\n */\nexport interface RsaEncryptParameters {\n  /**\n   * The encryption algorithm to use.\n   */\n  algorithm: RsaEncryptionAlgorithm;\n  /**\n   * The plain text to encrypt.\n   */\n  plaintext: Uint8Array;\n}\n\n/**\n * A union type representing all supported AES-GCM encryption algorithms.\n */\nexport type AesGcmEncryptionAlgorithm = \"A128GCM\" | \"A192GCM\" | \"A256GCM\";\n\n/**\n * Encryption parameters for AES-GCM encryption algorithms.\n */\nexport interface AesGcmEncryptParameters {\n  /**\n   * The encryption algorithm to use.\n   */\n  algorithm: AesGcmEncryptionAlgorithm;\n  /**\n   * The plain text to encrypt.\n   */\n  plaintext: Uint8Array;\n  /**\n   * Optional data that is authenticated but not encrypted.\n   */\n  additionalAuthenticatedData?: Uint8Array;\n}\n\n/**\n * A union type representing all supported AES-CBC encryption algorithms.\n */\nexport type AesCbcEncryptionAlgorithm =\n  | \"A128CBC\"\n  | \"A192CBC\"\n  | \"A256CBC\"\n  | \"A128CBCPAD\"\n  | \"A192CBCPAD\"\n  | \"A256CBCPAD\";\n\n/**\n * Encryption parameters for AES-CBC encryption algorithms.\n */\nexport interface AesCbcEncryptParameters {\n  /**\n   * The encryption algorithm to use.\n   */\n  algorithm: AesCbcEncryptionAlgorithm;\n  /**\n   * The plain text to encrypt.\n   */\n  plaintext: Uint8Array;\n  /**\n   * The initialization vector used for encryption. If omitted we will attempt to generate an IV using crypto's `randomBytes` functionality.\n   * An error will be thrown if creating an IV fails, and you may recover by passing in your own cryptographically secure IV.\n   *\n   * When passing your own IV, make sure you use a cryptographically random, non-repeating IV.\n   */\n  iv?: Uint8Array;\n}\n\n/**\n * A type representing all currently supported encryption parameters as they apply to different encryption algorithms.\n */\nexport type EncryptParameters =\n  | RsaEncryptParameters\n  | AesGcmEncryptParameters\n  | AesCbcEncryptParameters;\n\n/**\n * Decryption parameters for RSA encryption algorithms.\n */\nexport interface RsaDecryptParameters {\n  /**\n   * The encryption algorithm to use.\n   */\n  algorithm: RsaEncryptionAlgorithm;\n  /**\n   * The ciphertext to decrypt.\n   */\n  ciphertext: Uint8Array;\n}\n\n/**\n * Decryption parameters for AES-GCM encryption algorithms.\n */\nexport interface AesGcmDecryptParameters {\n  /**\n   * The encryption algorithm to use.\n   */\n  algorithm: AesGcmEncryptionAlgorithm;\n  /**\n   * The ciphertext to decrypt.\n   */\n  ciphertext: Uint8Array;\n  /**\n   * The initialization vector (or nonce) generated during encryption.\n   */\n  iv: Uint8Array;\n  /**\n   * The authentication tag generated during encryption.\n   */\n  authenticationTag: Uint8Array;\n  /**\n   * Optional data that is authenticated but not encrypted.\n   */\n  additionalAuthenticatedData?: Uint8Array;\n}\n\n/**\n * Decryption parameters for AES-CBC encryption algorithms.\n */\nexport interface AesCbcDecryptParameters {\n  /**\n   * The encryption algorithm to use.\n   */\n  algorithm: AesCbcEncryptionAlgorithm;\n  /**\n   * The initialization vector used during encryption.\n   */\n  /**\n   * The ciphertext to decrypt. Microsoft recommends you not use CBC without first ensuring the integrity of the ciphertext using an HMAC, for example.\n   * See https://learn.microsoft.com/dotnet/standard/security/vulnerabilities-cbc-mode for more information.\n   */\n  ciphertext: Uint8Array;\n  /**\n   * The initialization vector generated during encryption.\n   */\n  iv: Uint8Array;\n}\n\n/**\n * A type representing all currently supported decryption parameters as they apply to different encryption algorithms.\n */\nexport type DecryptParameters =\n  | RsaDecryptParameters\n  | AesGcmDecryptParameters\n  | AesCbcDecryptParameters;\n\n/**\n * The various key types a {@link CryptographyClient} can hold.\n * The key may be an identifier (URL) to a KeyVault key, the actual KeyVault key,\n * or a local-only JsonWebKey.\n *\n * If an identifier is used, an attempt will be made to exchange it for a {@link KeyVaultKey} during the first operation call. If this attempt fails, the identifier\n * will become a remote-only identifier and the {@link CryptographyClient} will only be able to perform remote operations.\n */\nexport type CryptographyClientKey =\n  | {\n      kind: \"identifier\";\n      value: string;\n    }\n  | {\n      kind: \"remoteOnlyIdentifier\";\n      value: string;\n    }\n  | {\n      kind: \"KeyVaultKey\";\n      value: KeyVaultKey;\n    }\n  | {\n      kind: \"JsonWebKey\";\n      value: JsonWebKey;\n    };\n"]}