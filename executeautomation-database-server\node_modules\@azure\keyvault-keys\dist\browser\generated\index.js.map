{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/generated/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAEL,mBAAmB,EAEnB,wBAAwB,EAGxB,0BAA0B,EAG1B,wBAAwB,EAcxB,kCAAkC,EAIlC,iCAAiC,EAKjC,2BAA2B,EAY3B,aAAa,GACd,MAAM,mBAAmB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport {\n  PageSettings,\n  ContinuablePage,\n  PagedAsyncIterableIterator,\n} from \"./static-helpers/pagingHelpers.js\";\n\nexport { KeyVaultClient } from \"./keyVaultClient.js\";\nexport {\n  KeyCreateParameters,\n  KnownJsonWebKeyType,\n  JsonWebKeyType,\n  KnownJsonWebKeyOperation,\n  JsonWebKeyOperation,\n  KeyAttributes,\n  KnownDeletionRecoveryLevel,\n  DeletionRecoveryLevel,\n  KeyAttestation,\n  KnownJsonWebKeyCurveName,\n  JsonWebKeyCurveName,\n  KeyReleasePolicy,\n  KeyBundle,\n  JsonWebKey,\n  KeyVaultError,\n  ErrorModel,\n  KeyImportParameters,\n  DeletedKeyBundle,\n  KeyUpdateParameters,\n  KeyItem,\n  BackupKeyResult,\n  KeyRestoreParameters,\n  KeyOperationsParameters,\n  KnownJsonWebKeyEncryptionAlgorithm,\n  JsonWebKeyEncryptionAlgorithm,\n  KeyOperationResult,\n  KeySignParameters,\n  KnownJsonWebKeySignatureAlgorithm,\n  JsonWebKeySignatureAlgorithm,\n  KeyVerifyParameters,\n  KeyVerifyResult,\n  KeyReleaseParameters,\n  KnownKeyEncryptionAlgorithm,\n  KeyEncryptionAlgorithm,\n  KeyReleaseResult,\n  DeletedKeyItem,\n  KeyRotationPolicy,\n  LifetimeActions,\n  LifetimeActionsTrigger,\n  LifetimeActionsType,\n  KeyRotationPolicyAction,\n  KeyRotationPolicyAttributes,\n  GetRandomBytesRequest,\n  RandomBytes,\n  KnownVersions,\n} from \"./models/index.js\";\nexport {\n  KeyVaultClientOptionalParams,\n  GetKeyAttestationOptionalParams,\n  GetRandomBytesOptionalParams,\n  UpdateKeyRotationPolicyOptionalParams,\n  GetKeyRotationPolicyOptionalParams,\n  RecoverDeletedKeyOptionalParams,\n  PurgeDeletedKeyOptionalParams,\n  GetDeletedKeyOptionalParams,\n  GetDeletedKeysOptionalParams,\n  ReleaseOptionalParams,\n  UnwrapKeyOptionalParams,\n  WrapKeyOptionalParams,\n  VerifyOptionalParams,\n  SignOptionalParams,\n  DecryptOptionalParams,\n  EncryptOptionalParams,\n  RestoreKeyOptionalParams,\n  BackupKeyOptionalParams,\n  GetKeysOptionalParams,\n  GetKeyVersionsOptionalParams,\n  GetKeyOptionalParams,\n  UpdateKeyOptionalParams,\n  DeleteKeyOptionalParams,\n  ImportKeyOptionalParams,\n  RotateKeyOptionalParams,\n  CreateKeyOptionalParams,\n} from \"./api/index.js\";\nexport { PageSettings, ContinuablePage, PagedAsyncIterableIterator };\n"]}