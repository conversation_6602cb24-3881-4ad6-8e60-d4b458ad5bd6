import type { Verify } from "node:crypto";
/**
 * @internal
 * Use the platform-local hashing functionality
 */
export declare function createHash(algorithm: string, data: Uint8Array): Promise<Buffer>;
/**
 * @internal
 * Use the platform-local verify functionality
 */
export declare function createVerify(algorithm: string, data: Uint8Array): Verify;
/**
 * @internal
 * Use the platform-local randomBytes functionality
 */
export declare function randomBytes(length: number): Uint8Array;
//# sourceMappingURL=crypto.d.ts.map