# FileScopeMCP Augment Code 导入配置指南

## 📋 概述

本文档详细介绍如何使用 Augment Code 的 "Import from JSON" 功能添加 FileScopeMCP 服务器。

**前提条件**: FileScopeMCP 已安装在 `F:\MCP\FileScopeMCP` 目录中

## 🚀 使用 Import from JSON 功能

### 方法一：使用 Augment 设置面板的 Import 功能

#### 步骤 1: 准备配置文件

已为您准备好配置文件：`F:\MCP\FileScopeMCP\augment_import_config_fixed.json`

**Import MCP Server 格式**（用于 Import from JSON 功能）：
```json
{
  "name": "FileScopeMCP",
  "command": "node",
  "args": [
    "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
    "--base-dir=F:\\MCP"
  ]
}
```

**完整 settings.json 格式**（用于手动编辑）：
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": [
          "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
          "--base-dir=F:\\MCP"
        ]
      }
    ]
  }
}
```

#### 步骤 2: 在 Augment Code 中导入

1. **打开 Augment Code**
   - 启动 Augment Code 应用程序

2. **访问设置面板**
   - 点击 Augment 面板右上角的齿轮图标 ⚙️
   - 或使用快捷键 `Cmd/Ctrl + Shift + P`，然后选择 "Settings"

3. **找到 MCP 服务器部分**
   - 在设置面板中找到 "MCP Servers" 或 "Tools" 部分

4. **使用 Import from JSON 功能**
   - 寻找 "Import from JSON" 或 "Import Configuration" 按钮
   - 点击该按钮

5. **选择配置文件**
   - 浏览到 `F:\MCP\FileScopeMCP\augment_import_config.json`
   - 选择并打开该文件

6. **确认导入**
   - 检查导入的配置信息是否正确
   - 确认 FileScopeMCP 服务器已添加到列表中

7. **保存并重启**
   - 保存设置
   - 重启 Augment Code 以加载新配置

### 方法二：手动编辑 settings.json（备选方案）

如果 Import from JSON 功能不可用，可以手动编辑配置：

#### 步骤 1: 访问 settings.json

1. **打开设置编辑器**
   - 按 `Cmd/Ctrl + Shift + P`
   - 选择 "Edit Settings"
   - 在 Advanced 下，点击 "Edit in settings.json"

#### 步骤 2: 添加配置

将以下配置添加到 `augment.advanced.mcpServers` 数组中：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": [
          "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
          "--base-dir=F:\\MCP"
        ]
      }
    ]
  }
}
```

**注意**: 如果已有其他 MCP 服务器，请将 FileScopeMCP 配置添加到现有数组中：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "existing-server",
        "command": "existing-command",
        "args": ["existing-args"]
      },
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": [
          "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
          "--base-dir=F:\\MCP"
        ]
      }
    ]
  }
}
```

#### 步骤 3: 保存并重启

1. **保存文件**
   - 使用 `Ctrl + S` 保存 settings.json
   - 检查 JSON 语法是否正确（无遗漏逗号或括号）

2. **重启 Augment Code**
   - 完全关闭 Augment Code
   - 重新启动应用程序

## 🔧 配置参数说明

### 基本参数

- **name**: `"FileScopeMCP"`
  - MCP 服务器的唯一标识名称
  - 在 Augment 界面中显示的名称

- **command**: `"node"`
  - 用于启动 MCP 服务器的命令
  - 使用 Node.js 运行 JavaScript 文件

- **args**: 命令参数数组
  - `"F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js"`: 服务器文件的绝对路径
  - `"--base-dir=F:\\MCP"`: 设置基础工作目录

### 路径配置重要说明

**Windows 路径格式**:
- 使用双反斜杠 `\\` 进行转义
- 或使用正斜杠 `/` (Node.js 兼容)
- 确保路径指向正确的文件位置

**示例路径格式**:
```json
"F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js"  // 推荐格式
"F:/MCP/FileScopeMCP/dist/mcp-server.js"      // 替代格式
```

## ✅ 验证安装

### 步骤 1: 检查 MCP 服务器状态

重启 Augment Code 后：

1. **查看设置面板**
   - 打开 Augment 设置
   - 确认 FileScopeMCP 出现在 MCP 服务器列表中
   - 检查状态是否为 "Active" 或 "Connected"

2. **查看日志信息**
   - 如果有错误，查看 Augment 的日志输出
   - 检查是否有连接或启动错误

### 步骤 2: 测试功能

在 Augment Code 中尝试以下命令：

```
请帮我分析 F:\MCP 项目的文件结构，找出最重要的文件
```

**期望结果**:
- FileScopeMCP 应该开始扫描项目目录
- 返回文件重要性分析结果
- 显示项目中的关键文件列表

```
为 F:\MCP 项目生成一个依赖关系图表
```

**期望结果**:
- 生成 Mermaid 格式的依赖关系图
- 可能输出为 HTML 或图片格式
- 显示文件间的依赖关系

## 🛠️ 故障排除

### 常见问题 1: 服务器无法启动

**症状**: Augment 显示 FileScopeMCP 连接失败

**解决方案**:
1. 检查 Node.js 是否已安装且在 PATH 中
2. 验证文件路径是否正确
3. 确认 `dist/mcp-server.js` 文件存在
4. 检查文件权限

**验证命令**:
```bash
cd F:\MCP\FileScopeMCP
node dist/mcp-server.js --help
```

### 常见问题 2: JSON 配置错误

**症状**: 设置保存失败或语法错误

**解决方案**:
1. 检查 JSON 语法是否正确
2. 确认所有括号和逗号匹配
3. 验证路径中的反斜杠转义

**正确格式检查**:
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": [
          "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
          "--base-dir=F:\\MCP"
        ]
      }
    ]
  }
}
```

### 常见问题 3: 路径问题

**症状**: 找不到文件或目录

**解决方案**:
1. 使用绝对路径而非相对路径
2. 确认路径中的反斜杠正确转义
3. 验证目录和文件确实存在

## 📚 更多资源

- **详细安装指南**: `F:\MCP\doc\FileScopeMCP_安装配置指南.md`
- **Augment MCP 通用指南**: `F:\MCP\doc\Augment_MCP_安装配置指南.md`
- **FileScopeMCP 官方文档**: `F:\MCP\doc\FileScopeMCP_官方使用指南.md`
- **快速安装说明**: `F:\MCP\FileScopeMCP\README_安装说明.md`

## 🎯 配置完成检查清单

- [ ] 配置文件已准备：`augment_import_config.json`
- [ ] 使用 Import from JSON 功能导入配置
- [ ] 或手动编辑 settings.json 添加配置
- [ ] 保存设置并重启 Augment Code
- [ ] 验证 FileScopeMCP 出现在 MCP 服务器列表中
- [ ] 测试基本功能：文件分析和依赖图生成
- [ ] 确认所有工具正常工作

---

💡 **提示**: 如果遇到问题，请检查 Augment Code 的日志输出，通常能提供详细的错误信息帮助诊断问题！
