# Tencent-TAT MCP Server 安装成功指南

## 概述

本文档记录了在Augment中成功安装和配置Tencent-TAT MCP Server的完整过程，包含常见错误及解决方案。

## 环境信息

- **操作系统**: Windows
- **Python版本**: 3.12.7
- **MCP版本**: 1.12.0
- **mcp-server-tat版本**: 0.1.5

## 安装步骤

### 1. 创建虚拟环境

```bash
cd F:\MCP\tat-mcp-server
python -m venv env
```

### 2. 激活虚拟环境

```bash
env\Scripts\activate.bat
```

### 3. 安装MCP服务器

```bash
pip install mcp-server-tat
```

### 4. 验证安装

```bash
python -c "import mcp_server_tat; print('✅ mcp_server_tat模块导入成功')"
```

## 配置文件设置

### 正确的Augment MCP配置

```json
{
  "mcpServers": {
    "tencent-tat": {
      "command": "F:/MCP/tat-mcp-server/env/Scripts/mcp-server-tat.exe",
      "args": [],
      "env": {
        "TENCENTCLOUD_SECRET_ID": "您的SecretId",
        "TENCENTCLOUD_SECRET_KEY": "您的SecretKey",
        "TENCENTCLOUD_REGION": "ap-shanghai",
        "DEFAULT_INSTANCE_ID": "lhins-m9amye7a"
      }
    }
  }
}
```

## 常见错误及解决方案

### ❌ 错误1: 模块导入失败

**错误信息**: 
```
No module named 'mcp_server_tat'
```

**原因**: 使用了错误的启动方式 `python.exe -m mcp_server_tat`

**解决方案**: 
- 使用可执行文件: `mcp-server-tat.exe`
- 配置中的 `command` 应该指向 `mcp-server-tat.exe`
- `args` 设置为空数组 `[]`

### ❌ 错误2: 命令格式错误

**错误信息**:
```
MCP error -32000: Connection closed
SyntaxError: unterminated string literal
```

**原因**: 配置中使用了错误的命令格式，如：
```json
"command": "python.exe -c \"from mcp_server_tat.server import server; server.run()\""
```

**解决方案**: 
使用正确的可执行文件路径：
```json
"command": "F:/MCP/tat-mcp-server/env/Scripts/mcp-server-tat.exe"
```

### ❌ 错误3: 实例ID格式错误

**错误信息**:
```
参数`InstanceId` 参数值不符合要求
```

**原因**: 使用了无效的实例ID格式

**解决方案**: 
- CVM实例: `ins-xxxxxxxx` 格式
- 轻量服务器: `lhins-xxxxxxxx` 格式
- 确保实例ID真实存在且已安装TAT Agent

### ❌ 错误4: TAT Agent未安装

**错误信息**:
```
The specified instance does not have agent installed or does not exist
```

**解决方案**: 
1. 在腾讯云控制台确认实例存在
2. 确保实例已安装TAT Agent
3. 检查实例状态是否正常运行

### ❌ 错误5: API密钥配置错误

**错误信息**:
```
AuthFailure.SecretIdNotFound
```

**解决方案**: 
1. 确保SecretId和SecretKey正确
2. 检查API密钥是否有TAT相关权限
3. 确认地域设置正确

## 验证安装成功

### 1. 运行验证脚本

```bash
cd tat-mcp-server
env\Scripts\python.exe verify_config.py
```

### 2. 测试MCP工具

在Augment中测试以下命令：
```
帮我查看云服务器状态
```

### 3. 检查可用工具

成功安装后应该可以使用：
- `SyncRunCommand_tencent-tat` - 同步执行命令
- `RunCommand_tencent-tat` - 异步执行命令
- `QueryTask_tencent-tat` - 查询任务结果

## 最佳实践

### 1. 环境变量设置

建议在MCP配置中添加以下环境变量：
- `TENCENTCLOUD_REGION`: 默认地域
- `DEFAULT_INSTANCE_ID`: 默认实例ID

### 2. 安全建议

- 不要在配置文件中硬编码API密钥
- 使用具有最小权限的API密钥
- 定期轮换API密钥

### 3. 目录结构

推荐的目录结构：
```
F:\MCP\tat-mcp-server\
├── env\                    # 虚拟环境
├── config.json            # 配置文件
├── verify_config.py       # 验证脚本
├── start.bat              # 启动脚本
└── test.bat               # 测试脚本
```

## 故障排除

### 1. 检查虚拟环境

```bash
env\Scripts\python.exe -m pip list | findstr mcp
```

### 2. 测试模块导入

```bash
env\Scripts\python.exe -c "import mcp_server_tat; print('OK')"
```

### 3. 检查可执行文件

```bash
dir env\Scripts\mcp-server-tat.exe
```

### 4. 测试连接

```bash
env\Scripts\mcp-server-tat.exe --help
```

## 成功标志

安装成功的标志：
1. ✅ 虚拟环境中包含 `mcp-server-tat` 包
2. ✅ 可执行文件 `mcp-server-tat.exe` 存在
3. ✅ 模块可以正常导入
4. ✅ MCP工具在Augment中可用
5. ✅ 能够成功执行云服务器命令

## 联系信息

如遇到其他问题，请检查：
1. 腾讯云TAT官方文档
2. MCP官方文档
3. 确保网络连接正常
4. 检查防火墙设置

---

**文档版本**: 1.0  
**最后更新**: 2025-01-21  
**适用版本**: mcp-server-tat 0.1.5+
