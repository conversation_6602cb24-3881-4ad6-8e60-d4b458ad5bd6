{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/generated/api/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EACL,cAAc,GAGf,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EACL,iBAAiB,EACjB,cAAc,EACd,uBAAuB,EACvB,oBAAoB,EACpB,iBAAiB,EACjB,eAAe,EACf,aAAa,EACb,cAAc,EACd,OAAO,EACP,SAAS,EACT,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,SAAS,EACT,OAAO,EACP,cAAc,EACd,MAAM,EACN,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,GACV,MAAM,iBAAiB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport {\n  createKeyVault,\n  KeyVaultContext,\n  KeyVaultClientOptionalParams,\n} from \"./keyVaultContext.js\";\nexport {\n  getKeyAttestation,\n  getRandomBytes,\n  updateKeyRotationPolicy,\n  getKeyRotationPolicy,\n  recoverDeletedKey,\n  purgeDeletedKey,\n  getDeletedKey,\n  getDeletedKeys,\n  release,\n  unwrapKey,\n  wrapKey,\n  verify,\n  sign,\n  decrypt,\n  encrypt,\n  restoreKey,\n  backupKey,\n  getKeys,\n  getKeyVersions,\n  getKey,\n  updateKey,\n  deleteKey,\n  importKey,\n  rotateKey,\n  createKey,\n} from \"./operations.js\";\nexport {\n  GetKeyAttestationOptionalParams,\n  GetRandomBytesOptionalParams,\n  UpdateKeyRotationPolicyOptionalParams,\n  GetKeyRotationPolicyOptionalParams,\n  RecoverDeletedKeyOptionalParams,\n  PurgeDeletedKeyOptionalParams,\n  GetDeletedKeyOptionalParams,\n  GetDeletedKeysOptionalParams,\n  ReleaseOptionalParams,\n  UnwrapKeyOptionalParams,\n  WrapKeyOptionalParams,\n  VerifyOptionalParams,\n  SignOptionalParams,\n  DecryptOptionalParams,\n  EncryptOptionalParams,\n  RestoreKeyOptionalParams,\n  BackupKeyOptionalParams,\n  GetKeysOptionalParams,\n  GetKeyVersionsOptionalParams,\n  GetKeyOptionalParams,\n  UpdateKeyOptionalParams,\n  DeleteKeyOptionalParams,\n  ImportKeyOptionalParams,\n  RotateKeyOptionalParams,\n  CreateKeyOptionalParams,\n} from \"./options.js\";\n"]}