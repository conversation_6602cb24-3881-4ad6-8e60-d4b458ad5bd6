{"version": 3, "file": "conversions.js", "sourceRoot": "", "sources": ["../../../src/cryptography/conversions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;;GAGG;AACH,SAAS,YAAY,CAAC,MAAc;IAClC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;QAClB,OAAO,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;SAAM,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;QACxB,OAAO,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;SAAM,IAAI,MAAM,GAAG,KAAK,EAAE,CAAC;QAC1B,OAAO,UAAU,CAAC,EAAE,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC;IACzD,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,MAAkB,EAAE,QAAgB;IACxD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAEpC,sCAAsC;IACtC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC;QACrB,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChD,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACb,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACrB,MAAM,GAAG,KAAK,CAAC;IACjB,CAAC;IAED,yCAAyC;IACzC,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAClD,MAAM,WAAW,GAAG,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAE7D,MAAM,YAAY,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IACjD,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;IAC3B,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IACnC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IAEnD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,YAAY,CAAC,YAA0B;IAC9C,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC7E,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;IAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7C,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,WAAW;IAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACtD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,oBAAoB,CAAC,cAAsB;IAClD,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,IAAI,IAAI,CAAC;YACf,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClD,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,GAAe;IAC7C,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;QACnB,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU;QAC7E,MAAM,cAAc,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;QAClD,MAAM,IAAI,kCAAkC,CAAC;QAC7C,MAAM,IAAI,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC/C,MAAM,IAAI,gCAAgC,CAAC;IAC7C,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACjE,CAAC;IAED,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,6BAA6B;AAC3D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { <PERSON>son<PERSON>eb<PERSON>ey } from \"../keysModels.js\";\n\n/**\n * @internal\n * Encodes a length of a packet in DER format\n */\nfunction encodeLength(length: number): Uint8Array {\n  if (length <= 127) {\n    return Uint8Array.of(length);\n  } else if (length < 256) {\n    return Uint8Array.of(0x81, length);\n  } else if (length < 65536) {\n    return Uint8Array.of(0x82, length >> 8, length & 0xff);\n  } else {\n    throw new Error(\"Unsupported length to encode\");\n  }\n}\n\n/**\n * @internal\n * Encodes a buffer for DER, as sets the id to the given id\n */\nfunction encodeBuffer(buffer: Uint8Array, bufferId: number): Uint8Array {\n  if (buffer.length === 0) {\n    return buffer;\n  }\n\n  let result = new Uint8Array(buffer);\n\n  // If the high bit is set, prepend a 0\n  if (result[0] & 0x80) {\n    const array = new Uint8Array(result.length + 1);\n    array[0] = 0;\n    array.set(result, 1);\n    result = array;\n  }\n\n  // Prepend the DER header for this buffer\n  const encodedLength = encodeLength(result.length);\n  const totalLength = 1 + encodedLength.length + result.length;\n\n  const outputBuffer = new Uint8Array(totalLength);\n  outputBuffer[0] = bufferId;\n  outputBuffer.set(encodedLength, 1);\n  outputBuffer.set(result, 1 + encodedLength.length);\n\n  return outputBuffer;\n}\n\nfunction makeSequence(encodedParts: Uint8Array[]): string {\n  const totalLength = encodedParts.reduce((sum, part) => sum + part.length, 0);\n  const sequence = new Uint8Array(totalLength);\n\n  for (let i = 0; i < encodedParts.length; i++) {\n    const previousLength = i > 0 ? encodedParts[i - 1].length : 0;\n    sequence.set(encodedParts[i], previousLength);\n  }\n\n  const full_encoded = encodeBuffer(sequence, 0x30); // SEQUENCE\n  return Buffer.from(full_encoded).toString(\"base64\");\n}\n\n/**\n * Fill in the PEM with 64 character lines as per RFC:\n *\n * \"To represent the encapsulated text of a PEM message, the encoding\n * function's output is delimited into text lines (using local\n * conventions), with each line except the last containing exactly 64\n * printable characters and the final line containing 64 or fewer\n * printable characters.\"\n */\nfunction formatBase64Sequence(base64Sequence: string): string {\n  const lines = base64Sequence.match(/.{1,64}/g);\n  let result = \"\";\n  if (lines) {\n    for (const line of lines) {\n      result += line;\n      result += \"\\n\";\n    }\n  } else {\n    throw new Error(\"Could not create correct PEM\");\n  }\n  return result;\n}\n\n/**\n * @internal\n * Encode a JWK to PEM format. To do so, it internally repackages the JWK as a DER\n * that is then encoded as a PEM.\n */\nexport function convertJWKtoPEM(key: JsonWebKey): string {\n  let result = \"\";\n\n  if (key.n && key.e) {\n    const parts = [key.n, key.e];\n    const encodedParts = parts.map((part) => encodeBuffer(part, 0x2)); // INTEGER\n    const base64Sequence = makeSequence(encodedParts);\n    result += \"-----BEGIN RSA PUBLIC KEY-----\\n\";\n    result += formatBase64Sequence(base64Sequence);\n    result += \"-----END RSA PUBLIC KEY-----\\n\";\n  }\n\n  if (!result.length) {\n    throw new Error(\"Unsupported key format for local operations\");\n  }\n\n  return result.slice(0, -1); // Removing the last new line\n}\n"]}