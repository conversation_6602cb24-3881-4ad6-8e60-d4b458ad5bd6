{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAUlC;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,KAAc,EAAE,cAAuB;IACrE,OAAO,CACL,cAAc,KAAK,WAAW;QAC9B,cAAc,KAAK,YAAY;QAC/B,CAAC,OAAO,KAAK,KAAK,QAAQ;YACxB,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,SAAS;YAC1B,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,KAAK,CAAC,iEAAiE,CAAC;gBACtF,IAAI;YACN,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,IAAI,CAAC,CAClB,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB,GACvB,qKAAqK,CAAC;AAExK;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,KAAa;IACtC,OAAO,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED,MAAM,cAAc,GAClB,gFAAgF,CAAC;AAEnF;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACtC,OAAO,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAwBD;;;;;;;;;;GAUG;AACH,SAAS,sCAAsC,CAC7C,cAA0C;IAE1C,MAAM,sBAAsB,mCACvB,cAAc,CAAC,OAAO,GACtB,cAAc,CAAC,IAAI,CACvB,CAAC;IACF,IACE,cAAc,CAAC,eAAe;QAC9B,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,MAAM,KAAK,CAAC,EAC/D,CAAC;QACD,OAAO,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/D,CAAC;SAAM,CAAC;QACN,OAAO,cAAc,CAAC,cAAc;YAClC,CAAC,iCACM,cAAc,CAAC,OAAO,KACzB,IAAI,EAAE,cAAc,CAAC,IAAI,IAE7B,CAAC,CAAC,sBAAsB,CAAC;IAC7B,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAC7B,YAAmC,EACnC,YAA8C;;IAE9C,MAAM,aAAa,GAAG,YAAY,CAAC,aAAa,CAAC;IAEjD,+EAA+E;IAC/E,+CAA+C;IAC/C,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC3C,uCACK,aAAa,KAChB,IAAI,EAAE,YAAY,CAAC,UAAU,IAC7B;IACJ,CAAC;IACD,MAAM,UAAU,GAAG,YAAY,IAAI,YAAY,CAAC,UAAU,CAAC;IAC3D,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,CAAC,CAAC;IACjD,MAAM,oBAAoB,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,CAAC,IAAI,CAAC;IAEnD,+EAA+E;IAC/E,IAAI,oBAAoB,KAAK,QAAQ,EAAE,CAAC;QACtC,uCACK,aAAa,KAChB,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAC/B,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IACnD;IACJ,CAAC;IAED,MAAM,eAAe,GACnB,CAAC,oBAAoB,KAAK,WAAW;QAClC,UAA8B,CAAC,IAAI,CAAC,eAAe,CAAC;QACvD,EAAE,CAAC;IACL,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAC1D,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAChD,CAAC;IACF,IAAI,oBAAoB,KAAK,UAAU,IAAI,kBAAkB,EAAE,CAAC;QAC9D,MAAM,aAAa,GACjB,MAAA,YAAY,CAAC,UAAU,mCAAK,EAA4C,CAAC;QAE3E,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC;gBACxC,aAAa,CAAC,GAAG,CAAC,GAAG,MAAA,YAAY,CAAC,UAAU,0CAAG,GAAG,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7C,aAAa,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QACD,OAAO,UAAU;YACf,CAAC,YAAY,CAAC,UAAU;YACxB,CAAC,aAAa;YACd,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC,MAAM,KAAK,CAAC;YACxD,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,aAAa,CAAC;IACpB,CAAC;IAED,OAAO,sCAAsC,CAAC;QAC5C,IAAI,EAAE,YAAY,CAAC,UAAU;QAC7B,OAAO,EAAE,aAAa;QACtB,eAAe,EAAE,UAAU;QAC3B,cAAc,EAAE,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,oBAAoB,CAAC;KAC/E,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CompositeMapper, FullOperationResponse, OperationResponseMap } from \"./interfaces.js\";\n\n/**\n * The union of all possible types for a primitive response body.\n * @internal\n */\nexport type BodyPrimitive = number | string | boolean | Date | Uint8Array | undefined | null;\n\n/**\n * A type guard for a primitive response body.\n * @param value - Value to test\n *\n * @internal\n */\nexport function isPrimitiveBody(value: unknown, mapperTypeName?: string): value is BodyPrimitive {\n  return (\n    mapperTypeName !== \"Composite\" &&\n    mapperTypeName !== \"Dictionary\" &&\n    (typeof value === \"string\" ||\n      typeof value === \"number\" ||\n      typeof value === \"boolean\" ||\n      mapperTypeName?.match(/^(Date|DateTime|DateTimeRfc1123|UnixTime|ByteArray|Base64Url)$/i) !==\n        null ||\n      value === undefined ||\n      value === null)\n  );\n}\n\nconst validateISODuration =\n  /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\n\n/**\n * Returns true if the given string is in ISO 8601 format.\n * @param value - The value to be validated for ISO 8601 duration format.\n * @internal\n */\nexport function isDuration(value: string): boolean {\n  return validateISODuration.test(value);\n}\n\nconst validUuidRegex =\n  /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i;\n\n/**\n * Returns true if the provided uuid is valid.\n *\n * @param uuid - The uuid that needs to be validated.\n *\n * @internal\n */\nexport function isValidUuid(uuid: string): boolean {\n  return validUuidRegex.test(uuid);\n}\n\n/**\n * Representation of parsed response headers and body coupled with information\n * about how to map them:\n * - whether the response body should be wrapped (typically if its type is primitive).\n * - whether the response is nullable so it can be null if the combination of\n *   the headers and the body is empty.\n */\ninterface ResponseObjectWithMetadata {\n  /** whether the mapper allows nullable body */\n  hasNullableType: boolean;\n  /** whether the response's body should be wrapped */\n  shouldWrapBody: boolean;\n  /** parsed headers of the response */\n  headers:\n    | {\n        [key: string]: unknown;\n      }\n    | undefined;\n  /** parsed body of the response */\n  body: any;\n}\n\n/**\n * Maps the response as follows:\n * - wraps the response body if needed (typically if its type is primitive).\n * - returns null if the combination of the headers and the body is empty.\n * - otherwise, returns the combination of the headers and the body.\n *\n * @param responseObject - a representation of the parsed response\n * @returns the response that will be returned to the user which can be null and/or wrapped\n *\n * @internal\n */\nfunction handleNullableResponseAndWrappableBody(\n  responseObject: ResponseObjectWithMetadata,\n): unknown | null {\n  const combinedHeadersAndBody = {\n    ...responseObject.headers,\n    ...responseObject.body,\n  };\n  if (\n    responseObject.hasNullableType &&\n    Object.getOwnPropertyNames(combinedHeadersAndBody).length === 0\n  ) {\n    return responseObject.shouldWrapBody ? { body: null } : null;\n  } else {\n    return responseObject.shouldWrapBody\n      ? {\n          ...responseObject.headers,\n          body: responseObject.body,\n        }\n      : combinedHeadersAndBody;\n  }\n}\n\n/**\n * Take a `FullOperationResponse` and turn it into a flat\n * response object to hand back to the consumer.\n * @param fullResponse - The processed response from the operation request\n * @param responseSpec - The response map from the OperationSpec\n *\n * @internal\n */\nexport function flattenResponse(\n  fullResponse: FullOperationResponse,\n  responseSpec: OperationResponseMap | undefined,\n): unknown {\n  const parsedHeaders = fullResponse.parsedHeaders;\n\n  // head methods never have a body, but we return a boolean set to body property\n  // to indicate presence/absence of the resource\n  if (fullResponse.request.method === \"HEAD\") {\n    return {\n      ...parsedHeaders,\n      body: fullResponse.parsedBody,\n    };\n  }\n  const bodyMapper = responseSpec && responseSpec.bodyMapper;\n  const isNullable = Boolean(bodyMapper?.nullable);\n  const expectedBodyTypeName = bodyMapper?.type.name;\n\n  /** If the body is asked for, we look at the expected body type to handle it */\n  if (expectedBodyTypeName === \"Stream\") {\n    return {\n      ...parsedHeaders,\n      blobBody: fullResponse.blobBody,\n      readableStreamBody: fullResponse.readableStreamBody,\n    };\n  }\n\n  const modelProperties =\n    (expectedBodyTypeName === \"Composite\" &&\n      (bodyMapper as CompositeMapper).type.modelProperties) ||\n    {};\n  const isPageableResponse = Object.keys(modelProperties).some(\n    (k) => modelProperties[k].serializedName === \"\",\n  );\n  if (expectedBodyTypeName === \"Sequence\" || isPageableResponse) {\n    const arrayResponse: { [key: string]: unknown } =\n      fullResponse.parsedBody ?? ([] as unknown as { [key: string]: unknown });\n\n    for (const key of Object.keys(modelProperties)) {\n      if (modelProperties[key].serializedName) {\n        arrayResponse[key] = fullResponse.parsedBody?.[key];\n      }\n    }\n\n    if (parsedHeaders) {\n      for (const key of Object.keys(parsedHeaders)) {\n        arrayResponse[key] = parsedHeaders[key];\n      }\n    }\n    return isNullable &&\n      !fullResponse.parsedBody &&\n      !parsedHeaders &&\n      Object.getOwnPropertyNames(modelProperties).length === 0\n      ? null\n      : arrayResponse;\n  }\n\n  return handleNullableResponseAndWrappableBody({\n    body: fullResponse.parsedBody,\n    headers: parsedHeaders,\n    hasNullableType: isNullable,\n    shouldWrapBody: isPrimitiveBody(fullResponse.parsedBody, expectedBodyTypeName),\n  });\n}\n"]}