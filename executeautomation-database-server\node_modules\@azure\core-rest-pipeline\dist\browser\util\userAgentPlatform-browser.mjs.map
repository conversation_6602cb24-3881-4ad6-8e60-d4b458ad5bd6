{"version": 3, "file": "userAgentPlatform-browser.mjs", "sourceRoot": "", "sources": ["../../../src/util/userAgentPlatform-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAgCD,SAAS,cAAc,CAAC,SAAiB;IACvC,MAAM,cAAc,GAAG;QACrB,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,mBAAmB,EAAE;QAC/C,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,2BAA2B,EAAE;KACvD,CAAC;IAEF,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;QACrC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QACpD,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAsB;IACnD,MAAM,UAAU,GAAG,CAAC,eAAe,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IACrF,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;QACzD,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAC,GAAwB;IACpE,MAAM,cAAc,GAAG,UAAU,CAAC,SAAwB,CAAC;IAC3D,IAAI,UAAU,GAAG,SAAS,CAAC;IAC3B,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,aAAa,EAAE,CAAC;QAClC,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAC5E,cAAc;YACd,iBAAiB;SAClB,CAAC,CAAC;QACH,UAAU,GAAG,GAAG,aAAa,CAAC,YAAY,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;QAExG,4BAA4B;QAC5B,MAAM,KAAK,GAAG,qBAAqB,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACzE,IAAI,KAAK,EAAE,CAAC;YACV,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;SAAM,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ,EAAE,CAAC;QACpC,UAAU,GAAG,cAAc,CAAC,QAAQ,CAAC;QACrC,MAAM,KAAK,GAAG,cAAc,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,KAAK,EAAE,CAAC;YACV,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;SAAM,IAAI,OAAO,UAAU,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;QACtD,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAED,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC5B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"x-ms-useragent\";\n}\n\ninterface BrowserBrand {\n  brand: string;\n  version: string;\n}\n\ninterface NavigatorEx extends Navigator {\n  userAgentData?: {\n    brands: BrowserBrand[];\n    mobile: boolean;\n    platform?: string;\n    getHighEntropyValues: (hints: string[]) => Promise<{\n      architecture: string;\n      bitness: string;\n      brands: BrowserBrand[];\n      formFactor: string;\n      fullVersionList: BrowserBrand[];\n      mobile: boolean;\n      model: string;\n      platform: string;\n      platformVersion: string;\n      wow64: boolean;\n    }>;\n  };\n}\n\ndeclare const globalThis: {\n  navigator?: NavigatorEx;\n  EdgeRuntime?: unknown;\n};\n\nfunction getBrowserInfo(userAgent: string): BrowserBrand | undefined {\n  const browserRegexes = [\n    { name: \"Firefox\", regex: /Firefox\\/([\\d.]+)/ },\n    { name: \"Safari\", regex: /Version\\/([\\d.]+).*Safari/ },\n  ];\n\n  for (const browser of browserRegexes) {\n    const match = userAgent.match(browser.regex);\n    if (match) {\n      return { brand: browser.name, version: match[1] };\n    }\n  }\n\n  return undefined;\n}\n\nfunction getBrandVersionString(brands: BrowserBrand[]): BrowserBrand | undefined {\n  const brandOrder = [\"Google Chrome\", \"Microsoft Edge\", \"Opera\", \"Brave\", \"Chromium\"];\n  for (const brand of brandOrder) {\n    const foundBrand = brands.find((b) => b.brand === brand);\n    if (foundBrand) {\n      return foundBrand;\n    }\n  }\n  return undefined;\n}\n\n/**\n * @internal\n */\nexport async function setPlatformSpecificData(map: Map<string, string>): Promise<void> {\n  const localNavigator = globalThis.navigator as NavigatorEx;\n  let osPlatform = \"unknown\";\n  if (localNavigator?.userAgentData) {\n    const entropyValues = await localNavigator.userAgentData.getHighEntropyValues([\n      \"architecture\",\n      \"platformVersion\",\n    ]);\n    osPlatform = `${entropyValues.architecture}-${entropyValues.platform}-${entropyValues.platformVersion}`;\n\n    // Get the brand and version\n    const brand = getBrandVersionString(localNavigator.userAgentData.brands);\n    if (brand) {\n      map.set(brand.brand, brand.version);\n    }\n  } else if (localNavigator?.platform) {\n    osPlatform = localNavigator.platform;\n    const brand = getBrowserInfo(localNavigator.userAgent);\n    if (brand) {\n      map.set(brand.brand, brand.version);\n    }\n  } else if (typeof globalThis.EdgeRuntime === \"string\") {\n    map.set(\"EdgeRuntime\", globalThis.EdgeRuntime);\n  }\n\n  map.set(\"OS\", osPlatform);\n}\n"]}