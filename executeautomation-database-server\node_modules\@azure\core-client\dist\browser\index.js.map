{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AACpE,OAAO,EAAE,aAAa,EAAwB,MAAM,oBAAoB,CAAC;AACzE,OAAO,EAAE,oBAAoB,EAAiC,MAAM,eAAe,CAAC;AACpF,OAAO,EA8BL,WAAW,EACX,WAAW,GAMZ,MAAM,iBAAiB,CAAC;AACzB,OAAO,EACL,qBAAqB,EACrB,yBAAyB,GAG1B,MAAM,4BAA4B,CAAC;AACpC,OAAO,EACL,mBAAmB,EACnB,uBAAuB,GAExB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,gCAAgC,EAAE,MAAM,uCAAuC,CAAC;AACzF,OAAO,EAAE,iCAAiC,EAAE,MAAM,wCAAwC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { createSerializer, MapperTypeNames } from \"./serializer.js\";\nexport { ServiceClient, ServiceClientOptions } from \"./serviceClient.js\";\nexport { createClientPipeline, InternalClientPipelineOptions } from \"./pipeline.js\";\nexport {\n  OperationSpec,\n  OperationArguments,\n  OperationOptions,\n  OperationResponseMap,\n  OperationParameter,\n  OperationQueryParameter,\n  OperationURLParameter,\n  Serializer,\n  BaseMapper,\n  Mapper,\n  MapperType,\n  SimpleMapperType,\n  EnumMapper,\n  EnumMapperType,\n  SequenceMapper,\n  SequenceMapperType,\n  DictionaryMapper,\n  DictionaryMapperType,\n  CompositeMapper,\n  CompositeMapperType,\n  MapperConstraints,\n  OperationRequest,\n  OperationRequestOptions,\n  OperationRequestInfo,\n  QueryCollectionFormat,\n  ParameterPath,\n  FullOperationResponse,\n  PolymorphicDiscriminator,\n  SpanConfig,\n  XML_ATTRKEY,\n  XML_CHARKEY,\n  XmlOptions,\n  SerializerOptions,\n  RawResponseCallback,\n  CommonClientOptions,\n  AdditionalPolicyConfig,\n} from \"./interfaces.js\";\nexport {\n  deserializationPolicy,\n  deserializationPolicyName,\n  DeserializationPolicyOptions,\n  DeserializationContentTypes,\n} from \"./deserializationPolicy.js\";\nexport {\n  serializationPolicy,\n  serializationPolicyName,\n  SerializationPolicyOptions,\n} from \"./serializationPolicy.js\";\nexport { authorizeRequestOnClaimChallenge } from \"./authorizeRequestOnClaimChallenge.js\";\nexport { authorizeRequestOnTenantChallenge } from \"./authorizeRequestOnTenantChallenge.js\";\n"]}