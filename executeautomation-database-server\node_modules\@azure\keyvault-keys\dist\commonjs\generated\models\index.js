"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnownVersions = exports.KnownKeyEncryptionAlgorithm = exports.KnownJsonWebKeySignatureAlgorithm = exports.KnownJsonWebKeyEncryptionAlgorithm = exports.KnownJsonWebKeyCurveName = exports.KnownDeletionRecoveryLevel = exports.KnownJsonWebKeyOperation = exports.KnownJsonWebKeyType = void 0;
var models_js_1 = require("./models.js");
Object.defineProperty(exports, "KnownJsonWebKeyType", { enumerable: true, get: function () { return models_js_1.KnownJsonWebKeyType; } });
Object.defineProperty(exports, "KnownJsonWebKeyOperation", { enumerable: true, get: function () { return models_js_1.KnownJsonWebKeyOperation; } });
Object.defineProperty(exports, "KnownDeletionRecoveryLevel", { enumerable: true, get: function () { return models_js_1.KnownDeletionRecoveryLevel; } });
Object.defineProperty(exports, "KnownJsonWebKeyCurveName", { enumerable: true, get: function () { return models_js_1.KnownJsonWebKeyCurveName; } });
Object.defineProperty(exports, "KnownJsonWebKeyEncryptionAlgorithm", { enumerable: true, get: function () { return models_js_1.KnownJsonWebKeyEncryptionAlgorithm; } });
Object.defineProperty(exports, "KnownJsonWebKeySignatureAlgorithm", { enumerable: true, get: function () { return models_js_1.KnownJsonWebKeySignatureAlgorithm; } });
Object.defineProperty(exports, "KnownKeyEncryptionAlgorithm", { enumerable: true, get: function () { return models_js_1.KnownKeyEncryptionAlgorithm; } });
Object.defineProperty(exports, "KnownVersions", { enumerable: true, get: function () { return models_js_1.KnownVersions; } });
//# sourceMappingURL=index.js.map