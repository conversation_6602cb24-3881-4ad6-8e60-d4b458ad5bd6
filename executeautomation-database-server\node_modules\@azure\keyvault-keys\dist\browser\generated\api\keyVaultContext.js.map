{"version": 3, "file": "keyVaultContext.js", "sourceRoot": "", "sources": ["../../../../src/generated/api/keyVaultContext.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAEtC,OAAO,EAAyB,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAiB3E,qHAAqH;AACrH,MAAM,UAAU,cAAc,CAC5B,aAAqB,EACrB,UAA2B,EAC3B,UAAwC,EAAE;;IAE1C,MAAM,WAAW,GACf,MAAA,MAAA,OAAO,CAAC,QAAQ,mCAAI,OAAO,CAAC,OAAO,mCAAI,MAAM,CAAC,aAAa,CAAC,CAAC;IAC/D,MAAM,iBAAiB,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,0CAAE,eAAe,CAAC;IACrE,MAAM,aAAa,GAAG,qCAAqC,CAAC;IAC5D,MAAM,eAAe,GAAG,iBAAiB;QACvC,CAAC,CAAC,GAAG,iBAAiB,iBAAiB,aAAa,EAAE;QACtD,CAAC,CAAC,gBAAgB,aAAa,EAAE,CAAC;IACpC,MAAM,qCACD,OAAO,KACV,gBAAgB,EAAE,EAAE,eAAe,EAAE,EACrC,cAAc,EAAE,EAAE,MAAM,EAAE,MAAA,MAAA,OAAO,CAAC,cAAc,0CAAE,MAAM,mCAAI,MAAM,CAAC,IAAI,EAAE,EACzE,WAAW,EAAE;YACX,MAAM,EAAE,MAAA,MAAA,OAAO,CAAC,WAAW,0CAAE,MAAM,mCAAI;gBACrC,kCAAkC;aACnC;SACF,GACF,EATK,EAAE,UAAU,EAAE,CAAC,OASpB,EATyB,cAAc,cAAlC,cAAoC,CASzC,CAAC;IACF,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACzE,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAClE,MAAM,UAAU,GAAG,MAAA,OAAO,CAAC,UAAU,mCAAI,KAAK,CAAC;IAC/C,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/B,IAAI,EAAE,wBAAwB;QAC9B,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACzB,qDAAqD;YACrD,yEAAyE;YACzE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAClB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GACzD,eAAe,UAAU,EAAE,CAAC;YAC9B,CAAC;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC,CAAC;IACH,OAAO,gCAAK,aAAa,KAAE,UAAU,GAAqB,CAAC;AAC7D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { logger } from \"../logger.js\";\nimport { KnownVersions } from \"../models/models.js\";\nimport { Client, ClientOptions, getClient } from \"@azure-rest/core-client\";\nimport { TokenCredential } from \"@azure/core-auth\";\n\n/** The key vault client performs cryptographic key operations and vault operations against the Key Vault service. */\nexport interface KeyVaultContext extends Client {\n  /** The API version to use for this operation. */\n  /** Known values of {@link KnownVersions} that the service accepts. */\n  apiVersion: string;\n}\n\n/** Optional parameters for the client. */\nexport interface KeyVaultClientOptionalParams extends ClientOptions {\n  /** The API version to use for this operation. */\n  /** Known values of {@link KnownVersions} that the service accepts. */\n  apiVersion?: string;\n}\n\n/** The key vault client performs cryptographic key operations and vault operations against the Key Vault service. */\nexport function createKeyVault(\n  endpointParam: string,\n  credential: TokenCredential,\n  options: KeyVaultClientOptionalParams = {},\n): KeyVaultContext {\n  const endpointUrl =\n    options.endpoint ?? options.baseUrl ?? String(endpointParam);\n  const prefixFromOptions = options?.userAgentOptions?.userAgentPrefix;\n  const userAgentInfo = `azsdk-js-keyvault-keys/1.0.0-beta.1`;\n  const userAgentPrefix = prefixFromOptions\n    ? `${prefixFromOptions} azsdk-js-api ${userAgentInfo}`\n    : `azsdk-js-api ${userAgentInfo}`;\n  const { apiVersion: _, ...updatedOptions } = {\n    ...options,\n    userAgentOptions: { userAgentPrefix },\n    loggingOptions: { logger: options.loggingOptions?.logger ?? logger.info },\n    credentials: {\n      scopes: options.credentials?.scopes ?? [\n        \"https://vault.azure.net/.default\",\n      ],\n    },\n  };\n  const clientContext = getClient(endpointUrl, credential, updatedOptions);\n  clientContext.pipeline.removePolicy({ name: \"ApiVersionPolicy\" });\n  const apiVersion = options.apiVersion ?? \"7.6\";\n  clientContext.pipeline.addPolicy({\n    name: \"ClientApiVersionPolicy\",\n    sendRequest: (req, next) => {\n      // Use the apiVersion defined in request url directly\n      // Append one if there is no apiVersion and we have one at client options\n      const url = new URL(req.url);\n      if (!url.searchParams.get(\"api-version\")) {\n        req.url = `${req.url}${\n          Array.from(url.searchParams.keys()).length > 0 ? \"&\" : \"?\"\n        }api-version=${apiVersion}`;\n      }\n\n      return next(req);\n    },\n  });\n  return { ...clientContext, apiVersion } as KeyVaultContext;\n}\n"]}