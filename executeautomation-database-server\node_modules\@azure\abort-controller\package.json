{"name": "@azure/abort-controller", "sdk-type": "client", "version": "2.1.2", "description": "Microsoft Azure SDK for JavaScript - Aborter", "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/core/abort-controller/README.md", "repository": "github:Azure/azure-sdk-for-js", "sideEffects": false, "type": "module", "main": "./dist/commonjs/index.js", "browser": "./dist/browser/index.js", "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "react-native": {"types": "./dist/react-native/index.d.ts", "default": "./dist/react-native/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "files": ["dist/", "README.md", "LICENSE"], "engines": {"node": ">=18.0.0"}, "keywords": ["azure", "aborter", "abortsignal", "cancellation", "node.js", "typescript", "javascript", "browser", "cloud"], "scripts": {"build:samples": "echo Obsolete", "build:test": "npm run clean && tshy && dev-tool run build-test", "build": "npm run clean && tshy && api-extractor run --local", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,mjs,cjs,json}\"", "clean": "rimraf --glob dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "tshy && api-extractor run --local", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,mjs,cjs,json}\"", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "lint:fix": "eslint package.json api-extractor.json src test --ext .ts --ext .cts --ext .mts --fix --fix-type [problem,suggestion]", "lint": "eslint package.json api-extractor.json src test --ext .ts --ext .cts --ext .mts", "pack": "npm pack 2>&1", "test:browser": "npm run clean && npm run build:test && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && tshy && npm run unit-test:node && npm run integration-test:node", "test": "npm run clean && tshy && npm run unit-test:node && dev-tool run build-test && npm run unit-test:browser && npm run integration-test", "unit-test:browser": "npm run build:test && dev-tool run test:vitest --no-test-proxy --browser", "unit-test:node": "dev-tool run test:vitest --no-test-proxy", "unit-test": "npm run unit-test:node && npm run unit-test:browser"}, "dependencies": {"tslib": "^2.6.2"}, "devDependencies": {"@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@microsoft/api-extractor": "^7.40.3", "@types/node": "^18.0.0", "@vitest/browser": "^1.3.1", "@vitest/coverage-istanbul": "^1.3.1", "eslint": "^8.56.0", "playwright": "^1.41.2", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tshy": "^1.13.0", "typescript": "~5.3.3", "vitest": "^1.3.1"}, "//metadata": {"migrationDate": "2023-03-08T18:36:03.000Z"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser", "react-native"], "selfLink": false}}