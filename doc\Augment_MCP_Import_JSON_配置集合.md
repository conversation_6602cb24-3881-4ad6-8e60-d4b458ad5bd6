# Augment MCP Import JSON 配置集合

## 📋 概述

本文档提供了各种 MCP 服务器的 Import from JSON 配置文件模板，可以直接复制使用或保存为 `.json` 文件导入到 Augment Code 中。

## 🎯 使用方法

1. **复制配置** - 选择需要的配置，复制到文本文件
2. **保存为 JSON** - 保存为 `.json` 扩展名文件
3. **导入到 Augment** - 使用 Import from JSON 功能导入
4. **重启应用** - 重启 Augment Code 生效

## 📁 已验证的配置

### 1. FileScopeMCP - 代码分析工具

**功能**: 项目文件分析、依赖关系可视化、重要性评分

**配置文件**: `filescopemcp-config.json`
```json
{
  "name": "FileScopeMCP",
  "command": "node",
  "args": [
    "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
    "--base-dir=F:\\MCP"
  ]
}
```

**测试命令**:
```
请帮我分析 F:\MCP 项目的文件结构，找出最重要的文件
```

### 2. GitHub MCP Server - GitHub 集成

**功能**: GitHub 仓库操作、Issue 管理、PR 处理

**配置文件**: `github-mcp-config.json`
```json
{
  "name": "GitHub",
  "command": "F:\\MCP\\github-mcp-server\\github-mcp-server.exe",
  "args": []
}
```

**测试命令**:
```
请列出我的 GitHub 仓库
```

### 3. Tencent TAT MCP Server - 腾讯云自动化助手

**功能**: 腾讯云服务器远程命令执行

**配置文件**: `tencent-tat-config.json`
```json
{
  "name": "TencentTAT",
  "command": "F:\\MCP\\tat-mcp-server\\env\\Scripts\\mcp-server-tat.exe",
  "args": []
}
```

**测试命令**:
```
请在腾讯云服务器上执行 ls 命令
```

### 4. SSH MCP Server - SSH 远程连接

**功能**: SSH 远程服务器连接和命令执行

**配置文件**: `ssh-mcp-config.json`
```json
{
  "name": "SSH",
  "command": "node",
  "args": [
    "F:\\MCP\\ssh-mcp-server\\src\\index.js"
  ]
}
```

**注意**: 需要先构建项目或使用 TypeScript 直接运行

## 🔧 通用配置模板

### Node.js 项目模板
```json
{
  "name": "YOUR_SERVER_NAME",
  "command": "node",
  "args": [
    "F:\\MCP\\YOUR_PROJECT\\dist\\server.js",
    "--option1=value1",
    "--option2=value2"
  ]
}
```

### Python 项目模板
```json
{
  "name": "YOUR_SERVER_NAME",
  "command": "F:\\MCP\\YOUR_PROJECT\\env\\Scripts\\python.exe",
  "args": [
    "F:\\MCP\\YOUR_PROJECT\\main.py"
  ]
}
```

### 可执行文件模板
```json
{
  "name": "YOUR_SERVER_NAME",
  "command": "F:\\MCP\\YOUR_PROJECT\\server.exe",
  "args": [
    "--config=config.json",
    "--verbose"
  ]
}
```

## 📝 配置文件命名规范

建议使用以下命名格式：
- `服务器名称-mcp-config.json`
- 例如：`filescopemcp-config.json`、`github-config.json`

## ⚠️ 配置注意事项

### 路径格式
- **Windows**: 使用双反斜杠 `\\` 或正斜杠 `/`
- **绝对路径**: 始终使用完整的绝对路径
- **路径验证**: 确保所有路径中的文件确实存在

### JSON 语法
- **引号**: 所有字符串必须用双引号包围
- **逗号**: 数组元素间用逗号分隔，最后一个元素后不加逗号
- **括号**: 确保所有括号正确匹配

### 参数格式
- **空参数**: 使用空数组 `"args": []`
- **多参数**: 每个参数作为数组的一个元素
- **参数值**: 包含空格的参数值需要用引号包围

## 🧪 配置验证

### 1. JSON 格式验证
使用在线 JSON 验证器检查语法：
- https://jsonlint.com/
- https://jsonformatter.curiousconcept.com/

### 2. 路径验证
在命令行中测试路径是否正确：
```bash
# 验证 Node.js 服务器
node "F:\MCP\FileScopeMCP\dist\mcp-server.js" --help

# 验证可执行文件
"F:\MCP\github-mcp-server\github-mcp-server.exe" --help
```

### 3. 导入测试
1. 保存配置为 `.json` 文件
2. 在 Augment Code 中导入
3. 检查是否出现在 MCP 服务器列表中
4. 重启 Augment Code
5. 测试服务器功能

## 🔄 批量配置

如果需要同时配置多个服务器，可以创建多个 JSON 文件，逐个导入：

**文件结构**:
```
mcp-configs/
├── filescopemcp-config.json
├── github-config.json
├── tencent-tat-config.json
└── ssh-config.json
```

**导入顺序**:
1. 先导入基础工具（如 FileScopeMCP）
2. 再导入云服务工具（如 Tencent TAT）
3. 最后导入集成工具（如 GitHub）

## 📊 配置状态检查

导入后检查以下状态：

### Augment Code 中的显示
- [ ] 服务器名称正确显示
- [ ] 状态显示为 "Active" 或 "Connected"
- [ ] 没有错误提示

### 功能测试
- [ ] 能够调用服务器功能
- [ ] 返回预期结果
- [ ] 没有连接错误

### 日志检查
- [ ] 启动日志正常
- [ ] 没有权限错误
- [ ] 没有路径错误

## 🚨 常见问题解决

### 问题 1: "Failed to parse MCP servers from JSON"
**解决**: 检查 JSON 语法，确保使用单服务器格式而非完整 settings.json 格式

### 问题 2: "Command not found"
**解决**: 验证命令路径是否正确，文件是否存在

### 问题 3: "Permission denied"
**解决**: 检查文件执行权限，可能需要管理员权限

### 问题 4: 服务器显示但无法使用
**解决**: 重启 Augment Code，检查服务器启动日志

## 📚 扩展配置

### 环境变量配置
某些服务器可能需要环境变量，可以通过启动脚本设置：

**Windows 批处理脚本** (`start-mcp.bat`):
```batch
@echo off
set API_KEY=your_api_key
set CONFIG_PATH=F:\MCP\config
node F:\MCP\your-server\dist\server.js
```

**配置文件**:
```json
{
  "name": "YourServer",
  "command": "F:\\MCP\\start-mcp.bat",
  "args": []
}
```

### 配置文件参数
```json
{
  "name": "ConfigurableServer",
  "command": "node",
  "args": [
    "F:\\MCP\\server\\dist\\server.js",
    "--config=F:\\MCP\\server\\config.json",
    "--log-level=info",
    "--port=3000"
  ]
}
```

---

💡 **提示**: 保存这些配置文件到一个专门的文件夹中，便于管理和备份！
