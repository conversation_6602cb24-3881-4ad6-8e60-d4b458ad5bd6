import { Client, ClientOptions } from "@azure-rest/core-client";
import { TokenCredential } from "@azure/core-auth";
/** The key vault client performs cryptographic key operations and vault operations against the Key Vault service. */
export interface KeyVaultContext extends Client {
    /** The API version to use for this operation. */
    /** Known values of {@link KnownVersions} that the service accepts. */
    apiVersion: string;
}
/** Optional parameters for the client. */
export interface KeyVaultClientOptionalParams extends ClientOptions {
    /** The API version to use for this operation. */
    /** Known values of {@link KnownVersions} that the service accepts. */
    apiVersion?: string;
}
/** The key vault client performs cryptographic key operations and vault operations against the Key Vault service. */
export declare function createKeyVault(endpointParam: string, credential: TokenCredential, options?: KeyVaultClientOptionalParams): KeyVaultContext;
//# sourceMappingURL=keyVaultContext.d.ts.map