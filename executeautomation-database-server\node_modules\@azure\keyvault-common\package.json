{"name": "@azure/keyvault-common", "version": "2.0.0", "description": "Common internal functionality for all of the Azure Key Vault clients in the Azure SDK for JavaScript", "sdk-type": "client", "author": "Microsoft Corporation", "license": "MIT", "main": "./dist/commonjs/index.js", "browser": "./dist/browser/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "scripts": {"audit": "node ../../../common/scripts/rush-audit.js && rimraf node_modules package-lock.json && npm i --package-lock-only 2>&1 && npm audit", "build": "npm run clean && dev-tool run build-package && dev-tool run extract-api", "build:samples": "echo skipped", "build:test": "npm run clean && dev-tool run build-package", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"*.{js,json}\"", "clean": "rimraf --glob dist dist-* temp types *.tgz *.log", "execute:samples": "dev-tool samples run samples-dev", "extract-api": "dev-tool run build-package && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"*.{js,json}\"", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "lint": "eslint README.md package.json api-extractor.json src test", "lint:fix": "eslint README.md package.json api-extractor.json src test --fix --fix-type [problem,suggestion]", "pack": "npm pack 2>&1", "test": "npm run build:test && npm run unit-test:node && dev-tool run bundle && npm run unit-test:browser && npm run integration-test", "test:browser": "npm run clean && npm run build:test && npm run integration-test:browser", "test:node": "npm run clean && tsc -p . && npm run integration-test:node", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "echo skipped", "unit-test:node": "dev-tool run test:vitest --no-test-proxy", "update-snippets": "echo skipped"}, "files": ["dist/", "README.md", "LICENSE"], "repository": "github:Azure/azure-sdk-for-js", "engines": {"node": ">=18.0.0"}, "keywords": ["azure", "cloud", "typescript"], "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/keyvault/keyvault-common/README.md", "sideEffects": false, "prettier": "@azure/eslint-plugin-azure-sdk/prettier.json", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.3.0", "@azure/core-client": "^1.5.0", "@azure/core-rest-pipeline": "^1.8.0", "@azure/core-tracing": "^1.0.0", "@azure/logger": "^1.1.4", "tslib": "^2.2.0", "@azure/core-util": "^1.10.0"}, "devDependencies": {"@azure-tools/test-utils-vitest": "^1.0.0", "@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@microsoft/api-extractor": "^7.31.1", "@types/node": "^18.0.0", "@vitest/browser": "^2.0.5", "@vitest/coverage-istanbul": "^2.0.5", "cross-env": "^7.0.2", "eslint": "^9.9.0", "playwright": "^1.46.0", "rimraf": "^5.0.5", "typescript": "~5.6.2", "vitest": "^2.0.5"}, "type": "module", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser"], "selfLink": false}, "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}}