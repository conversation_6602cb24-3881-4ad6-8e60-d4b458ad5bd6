import type { PipelinePolicy } from "../pipeline.js";
/**
 * The programmatic identifier of the userAgentPolicy.
 */
export declare const userAgentPolicyName = "userAgentPolicy";
/**
 * Options for adding user agent details to outgoing requests.
 */
export interface UserAgentPolicyOptions {
    /**
     * String prefix to add to the user agent for outgoing requests.
     * Defaults to an empty string.
     */
    userAgentPrefix?: string;
}
/**
 * A policy that sets the User-Agent header (or equivalent) to reflect
 * the library version.
 * @param options - Options to customize the user agent value.
 */
export declare function userAgentPolicy(options?: UserAgentPolicyOptions): PipelinePolicy;
//# sourceMappingURL=userAgentPolicy.d.ts.map