{"version": 3, "file": "urlHelpers.js", "sourceRoot": "", "sources": ["../../src/urlHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,sCAAsC,EAAE,MAAM,uBAAuB,CAAC;AAC/E,OAAO,EAAE,0BAA0B,EAAE,MAAM,uBAAuB,CAAC;AAEnE,MAAM,8BAA8B,GAA+C;IACjF,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,KAAK,EAAE,OAAO;IACd,GAAG,EAAE,IAAI;IACT,KAAK,EAAE,GAAG;CACX,CAAC;AAEF,MAAM,UAAU,aAAa,CAC3B,OAAe,EACf,aAA4B,EAC5B,kBAAsC,EACtC,cAAgD;IAEhD,MAAM,eAAe,GAAG,wBAAwB,CAC9C,aAAa,EACb,kBAAkB,EAClB,cAAc,CACf,CAAC;IAEF,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,IAAI,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACtD,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,IAAI,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAC3D,4DAA4D;QAC5D,4EAA4E;QAC5E,uBAAuB;QACvB,IAAI,aAAa,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACjE,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QACD,2DAA2D;QAC3D,2DAA2D;QAC3D,sBAAsB;QACtB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,UAAU,GAAG,IAAI,CAAC;YAClB,cAAc,GAAG,IAAI,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,wBAAwB,CAC9D,aAAa,EACb,kBAAkB,EAClB,cAAc,CACf,CAAC;IACF;;;;;OAKG;IACH,UAAU,GAAG,iBAAiB,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAExF,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,UAAU,CAAC,KAAa,EAAE,YAAiC;IAClE,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,KAAK,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,YAAY,EAAE,CAAC;QACvD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAC/B,aAA4B,EAC5B,kBAAsC,EACtC,cAAgD;;IAEhD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkB,CAAC;IACzC,IAAI,MAAA,aAAa,CAAC,aAAa,0CAAE,MAAM,EAAE,CAAC;QACxC,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,aAAa,EAAE,CAAC;YACvD,IAAI,iBAAiB,GAAW,sCAAsC,CACpE,kBAAkB,EAClB,YAAY,EACZ,cAAc,CACf,CAAC;YACF,MAAM,mBAAmB,GAAG,0BAA0B,CAAC,YAAY,CAAC,CAAC;YACrE,iBAAiB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACpD,YAAY,CAAC,MAAM,EACnB,iBAAiB,EACjB,mBAAmB,CACpB,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;gBAC/B,iBAAiB,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;YAC5D,CAAC;YACD,MAAM,CAAC,GAAG,CACR,IAAI,YAAY,CAAC,MAAM,CAAC,cAAc,IAAI,mBAAmB,GAAG,EAChE,iBAAiB,CAClB,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,aAAa,CAAC,GAAW;IAChC,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,YAAqB;IACpD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;IAEjC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;IAC1B,CAAC;IAED,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACjC,YAAY,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC9C,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACvD,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC;QACzB,IAAI,MAAM,EAAE,CAAC;YACX,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;QACjF,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,OAAO,GAAG,YAAY,CAAC;IACnC,CAAC;IAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAE7B,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,wBAAwB,CAC/B,aAA4B,EAC5B,kBAAsC,EACtC,cAAgD;;IAKhD,MAAM,MAAM,GAAG,IAAI,GAAG,EAA6B,CAAC;IACpD,MAAM,cAAc,GAAgB,IAAI,GAAG,EAAU,CAAC;IAEtD,IAAI,MAAA,aAAa,CAAC,eAAe,0CAAE,MAAM,EAAE,CAAC;QAC1C,KAAK,MAAM,cAAc,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;YAC3D,IAAI,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC3F,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,mBAAmB,GAAsB,sCAAsC,CACjF,kBAAkB,EAClB,cAAc,EACd,cAAc,CACf,CAAC;YACF,IACE,CAAC,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI,CAAC;gBACnE,cAAc,CAAC,MAAM,CAAC,QAAQ,EAC9B,CAAC;gBACD,mBAAmB,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CACtD,cAAc,CAAC,MAAM,EACrB,mBAAmB,EACnB,0BAA0B,CAAC,cAAc,CAAC,CAC3C,CAAC;gBAEF,MAAM,SAAS,GAAG,cAAc,CAAC,gBAAgB;oBAC/C,CAAC,CAAC,8BAA8B,CAAC,cAAc,CAAC,gBAAgB,CAAC;oBACjE,CAAC,CAAC,EAAE,CAAC;gBACP,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACvC,6BAA6B;oBAC7B,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;wBACrD,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;4BACxC,OAAO,EAAE,CAAC;wBACZ,CAAC;wBAED,OAAO,IAAI,CAAC;oBACd,CAAC,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,cAAc,CAAC,gBAAgB,KAAK,OAAO,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACpF,SAAS;gBACX,CAAC;qBAAM,IACL,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;oBAClC,CAAC,cAAc,CAAC,gBAAgB,KAAK,KAAK,IAAI,cAAc,CAAC,gBAAgB,KAAK,KAAK,CAAC,EACxF,CAAC;oBACD,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5D,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;oBACjC,IAAI,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;wBACvC,mBAAmB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE;4BAC7D,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;wBAClC,CAAC,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACN,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;gBAED,oEAAoE;gBACpE,IACE,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC;oBAClC,CAAC,cAAc,CAAC,gBAAgB,KAAK,KAAK,IAAI,cAAc,CAAC,gBAAgB,KAAK,OAAO,CAAC,EAC1F,CAAC;oBACD,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC5D,CAAC;gBAED,MAAM,CAAC,GAAG,CACR,cAAc,CAAC,MAAM,CAAC,cAAc,IAAI,0BAA0B,CAAC,cAAc,CAAC,EAClF,mBAAmB,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO;QACL,WAAW,EAAE,MAAM;QACnB,cAAc;KACf,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,WAAmB;IACjD,MAAM,MAAM,GAA+C,IAAI,GAAG,EAG/D,CAAC;IACJ,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,uBAAuB;IACvB,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAErC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBACjC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,iBAAiB,CAC/B,GAAW,EACX,WAA2C,EAC3C,cAA2B,EAC3B,cAAuB,KAAK;IAE5B,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QAC3B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,gFAAgF;IAChF,kFAAkF;IAClF,qFAAqF;IACrF,MAAM,cAAc,GAAG,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAEhE,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;gBACxC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,IAAI,aAAa,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/B,CAAC;iBAAM,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;YACnD,CAAC;YACD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,EAAE,CAAC;QAC3C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;QACxC,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChC,wEAAwE;YACxE,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;gBAC7B,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,QAAQ,EAAE,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,6FAA6F;IAC7F,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3E,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationArguments, OperationSpec, QueryCollectionFormat } from \"./interfaces.js\";\nimport { getOperationArgumentValueFromParameter } from \"./operationHelpers.js\";\nimport { getPathStringFromParameter } from \"./interfaceHelpers.js\";\n\nconst CollectionFormatToDelimiterMap: { [key in QueryCollectionFormat]: string } = {\n  CSV: \",\",\n  SSV: \" \",\n  Multi: \"Multi\",\n  TSV: \"\\t\",\n  Pipes: \"|\",\n};\n\nexport function getRequestUrl(\n  baseUri: string,\n  operationSpec: OperationSpec,\n  operationArguments: OperationArguments,\n  fallbackObject: { [parameterName: string]: any },\n): string {\n  const urlReplacements = calculateUrlReplacements(\n    operationSpec,\n    operationArguments,\n    fallbackObject,\n  );\n\n  let isAbsolutePath = false;\n\n  let requestUrl = replaceAll(baseUri, urlReplacements);\n  if (operationSpec.path) {\n    let path = replaceAll(operationSpec.path, urlReplacements);\n    // QUIRK: sometimes we get a path component like /{nextLink}\n    // which may be a fully formed URL with a leading /. In that case, we should\n    // remove the leading /\n    if (operationSpec.path === \"/{nextLink}\" && path.startsWith(\"/\")) {\n      path = path.substring(1);\n    }\n    // QUIRK: sometimes we get a path component like {nextLink}\n    // which may be a fully formed URL. In that case, we should\n    // ignore the baseUri.\n    if (isAbsoluteUrl(path)) {\n      requestUrl = path;\n      isAbsolutePath = true;\n    } else {\n      requestUrl = appendPath(requestUrl, path);\n    }\n  }\n\n  const { queryParams, sequenceParams } = calculateQueryParameters(\n    operationSpec,\n    operationArguments,\n    fallbackObject,\n  );\n  /**\n   * Notice that this call sets the `noOverwrite` parameter to true if the `requestUrl`\n   * is an absolute path. This ensures that existing query parameter values in `requestUrl`\n   * do not get overwritten. On the other hand when `requestUrl` is not absolute path, it\n   * is still being built so there is nothing to overwrite.\n   */\n  requestUrl = appendQueryParams(requestUrl, queryParams, sequenceParams, isAbsolutePath);\n\n  return requestUrl;\n}\n\nfunction replaceAll(input: string, replacements: Map<string, string>): string {\n  let result = input;\n  for (const [searchValue, replaceValue] of replacements) {\n    result = result.split(searchValue).join(replaceValue);\n  }\n  return result;\n}\n\nfunction calculateUrlReplacements(\n  operationSpec: OperationSpec,\n  operationArguments: OperationArguments,\n  fallbackObject: { [parameterName: string]: any },\n): Map<string, string> {\n  const result = new Map<string, string>();\n  if (operationSpec.urlParameters?.length) {\n    for (const urlParameter of operationSpec.urlParameters) {\n      let urlParameterValue: string = getOperationArgumentValueFromParameter(\n        operationArguments,\n        urlParameter,\n        fallbackObject,\n      );\n      const parameterPathString = getPathStringFromParameter(urlParameter);\n      urlParameterValue = operationSpec.serializer.serialize(\n        urlParameter.mapper,\n        urlParameterValue,\n        parameterPathString,\n      );\n      if (!urlParameter.skipEncoding) {\n        urlParameterValue = encodeURIComponent(urlParameterValue);\n      }\n      result.set(\n        `{${urlParameter.mapper.serializedName || parameterPathString}}`,\n        urlParameterValue,\n      );\n    }\n  }\n  return result;\n}\n\nfunction isAbsoluteUrl(url: string): boolean {\n  return url.includes(\"://\");\n}\n\nfunction appendPath(url: string, pathToAppend?: string): string {\n  if (!pathToAppend) {\n    return url;\n  }\n\n  const parsedUrl = new URL(url);\n  let newPath = parsedUrl.pathname;\n\n  if (!newPath.endsWith(\"/\")) {\n    newPath = `${newPath}/`;\n  }\n\n  if (pathToAppend.startsWith(\"/\")) {\n    pathToAppend = pathToAppend.substring(1);\n  }\n\n  const searchStart = pathToAppend.indexOf(\"?\");\n  if (searchStart !== -1) {\n    const path = pathToAppend.substring(0, searchStart);\n    const search = pathToAppend.substring(searchStart + 1);\n    newPath = newPath + path;\n    if (search) {\n      parsedUrl.search = parsedUrl.search ? `${parsedUrl.search}&${search}` : search;\n    }\n  } else {\n    newPath = newPath + pathToAppend;\n  }\n\n  parsedUrl.pathname = newPath;\n\n  return parsedUrl.toString();\n}\n\nfunction calculateQueryParameters(\n  operationSpec: OperationSpec,\n  operationArguments: OperationArguments,\n  fallbackObject: { [parameterName: string]: any },\n): {\n  queryParams: Map<string, string | string[]>;\n  sequenceParams: Set<string>;\n} {\n  const result = new Map<string, string | string[]>();\n  const sequenceParams: Set<string> = new Set<string>();\n\n  if (operationSpec.queryParameters?.length) {\n    for (const queryParameter of operationSpec.queryParameters) {\n      if (queryParameter.mapper.type.name === \"Sequence\" && queryParameter.mapper.serializedName) {\n        sequenceParams.add(queryParameter.mapper.serializedName);\n      }\n      let queryParameterValue: string | string[] = getOperationArgumentValueFromParameter(\n        operationArguments,\n        queryParameter,\n        fallbackObject,\n      );\n      if (\n        (queryParameterValue !== undefined && queryParameterValue !== null) ||\n        queryParameter.mapper.required\n      ) {\n        queryParameterValue = operationSpec.serializer.serialize(\n          queryParameter.mapper,\n          queryParameterValue,\n          getPathStringFromParameter(queryParameter),\n        );\n\n        const delimiter = queryParameter.collectionFormat\n          ? CollectionFormatToDelimiterMap[queryParameter.collectionFormat]\n          : \"\";\n        if (Array.isArray(queryParameterValue)) {\n          // replace null and undefined\n          queryParameterValue = queryParameterValue.map((item) => {\n            if (item === null || item === undefined) {\n              return \"\";\n            }\n\n            return item;\n          });\n        }\n        if (queryParameter.collectionFormat === \"Multi\" && queryParameterValue.length === 0) {\n          continue;\n        } else if (\n          Array.isArray(queryParameterValue) &&\n          (queryParameter.collectionFormat === \"SSV\" || queryParameter.collectionFormat === \"TSV\")\n        ) {\n          queryParameterValue = queryParameterValue.join(delimiter);\n        }\n        if (!queryParameter.skipEncoding) {\n          if (Array.isArray(queryParameterValue)) {\n            queryParameterValue = queryParameterValue.map((item: string) => {\n              return encodeURIComponent(item);\n            });\n          } else {\n            queryParameterValue = encodeURIComponent(queryParameterValue);\n          }\n        }\n\n        // Join pipes and CSV *after* encoding, or the server will be upset.\n        if (\n          Array.isArray(queryParameterValue) &&\n          (queryParameter.collectionFormat === \"CSV\" || queryParameter.collectionFormat === \"Pipes\")\n        ) {\n          queryParameterValue = queryParameterValue.join(delimiter);\n        }\n\n        result.set(\n          queryParameter.mapper.serializedName || getPathStringFromParameter(queryParameter),\n          queryParameterValue,\n        );\n      }\n    }\n  }\n  return {\n    queryParams: result,\n    sequenceParams,\n  };\n}\n\nfunction simpleParseQueryParams(queryString: string): Map<string, string | string[] | undefined> {\n  const result: Map<string, string | string[] | undefined> = new Map<\n    string,\n    string | string[] | undefined\n  >();\n  if (!queryString || queryString[0] !== \"?\") {\n    return result;\n  }\n\n  // remove the leading ?\n  queryString = queryString.slice(1);\n  const pairs = queryString.split(\"&\");\n\n  for (const pair of pairs) {\n    const [name, value] = pair.split(\"=\", 2);\n    const existingValue = result.get(name);\n    if (existingValue) {\n      if (Array.isArray(existingValue)) {\n        existingValue.push(value);\n      } else {\n        result.set(name, [existingValue, value]);\n      }\n    } else {\n      result.set(name, value);\n    }\n  }\n\n  return result;\n}\n\n/** @internal */\nexport function appendQueryParams(\n  url: string,\n  queryParams: Map<string, string | string[]>,\n  sequenceParams: Set<string>,\n  noOverwrite: boolean = false,\n): string {\n  if (queryParams.size === 0) {\n    return url;\n  }\n\n  const parsedUrl = new URL(url);\n\n  // QUIRK: parsedUrl.searchParams will have their name/value pairs decoded, which\n  // can change their meaning to the server, such as in the case of a SAS signature.\n  // To avoid accidentally un-encoding a query param, we parse the key/values ourselves\n  const combinedParams = simpleParseQueryParams(parsedUrl.search);\n\n  for (const [name, value] of queryParams) {\n    const existingValue = combinedParams.get(name);\n    if (Array.isArray(existingValue)) {\n      if (Array.isArray(value)) {\n        existingValue.push(...value);\n        const valueSet = new Set(existingValue);\n        combinedParams.set(name, Array.from(valueSet));\n      } else {\n        existingValue.push(value);\n      }\n    } else if (existingValue) {\n      if (Array.isArray(value)) {\n        value.unshift(existingValue);\n      } else if (sequenceParams.has(name)) {\n        combinedParams.set(name, [existingValue, value]);\n      }\n      if (!noOverwrite) {\n        combinedParams.set(name, value);\n      }\n    } else {\n      combinedParams.set(name, value);\n    }\n  }\n\n  const searchPieces: string[] = [];\n  for (const [name, value] of combinedParams) {\n    if (typeof value === \"string\") {\n      searchPieces.push(`${name}=${value}`);\n    } else if (Array.isArray(value)) {\n      // QUIRK: If we get an array of values, include multiple key/value pairs\n      for (const subValue of value) {\n        searchPieces.push(`${name}=${subValue}`);\n      }\n    } else {\n      searchPieces.push(`${name}=${value}`);\n    }\n  }\n\n  // QUIRK: we have to set search manually as searchParams will encode comma when it shouldn't.\n  parsedUrl.search = searchPieces.length ? `?${searchPieces.join(\"&\")}` : \"\";\n  return parsedUrl.toString();\n}\n"]}