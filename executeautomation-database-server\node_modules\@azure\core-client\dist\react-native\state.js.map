{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../src/state.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,yFAAyF;AACzF,wGAAwG;AACxG,OAAO,EAAE,KAAK,IAAI,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAEzD;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,QAEpB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationRequest, OperationRequestInfo } from \"./interfaces.js\";\n\n// @ts-expect-error The recommended approach to sharing module state between ESM and CJS.\n// See https://github.com/isaacs/tshy/blob/main/README.md#module-local-state for additional information.\nimport { state as cjsState } from \"../commonjs/state.js\";\n\n/**\n * Defines the shared state between CJS and ESM by re-exporting the CJS state.\n */\nexport const state = cjsState as {\n  operationRequestMap: WeakMap<OperationRequest, OperationRequestInfo>;\n};\n"]}