{"version": 3, "file": "poller.js", "sourceRoot": "", "sources": ["../../../../src/lro/recover/poller.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,8BAA8B,EAAE,MAAM,gBAAgB,CAAC;AAGhE,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,iBAG5C;IACC,YAAY,OAAiC;QAC3C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,YAAY,GAAG,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAEpF,IAAI,KAAsD,CAAC;QAE3D,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;QACvC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,8BAA8B,iCAE7C,KAAK,KACR,IAAI,KAEN,MAAM,EACN,gBAAgB,CACjB,CAAC;QAEF,KAAK,CAAC,SAAS,CAAC,CAAC;QAEjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { RecoverDeletedKeyPollOperationState } from \"./operation.js\";\nimport { RecoverDeletedKeyPollOperation } from \"./operation.js\";\nimport type { KeyVaultKey } from \"../../keysModels.js\";\nimport type { KeyVaultKeyPollerOptions } from \"../keyVaultKeyPoller.js\";\nimport { KeyVaultKeyPoller } from \"../keyVaultKeyPoller.js\";\n\n/**\n * Class that deletes a poller that waits until a key finishes being deleted\n */\nexport class RecoverDeletedKeyPoller extends KeyVaultKeyPoller<\n  RecoverDeletedKeyPollOperationState,\n  KeyVaultKey\n> {\n  constructor(options: KeyVaultKeyPollerOptions) {\n    const { client, name, operationOptions, intervalInMs = 2000, resumeFrom } = options;\n\n    let state: RecoverDeletedKeyPollOperationState | undefined;\n\n    if (resumeFrom) {\n      state = JSON.parse(resumeFrom).state;\n    }\n\n    const operation = new RecoverDeletedKeyPollOperation(\n      {\n        ...state,\n        name,\n      },\n      client,\n      operationOptions,\n    );\n\n    super(operation);\n\n    this.intervalInMs = intervalInMs;\n  }\n}\n"]}