# FileScopeMCP 官方使用指南

## 📊 项目概况

**GitHub 仓库**: [admica/FileScopeMCP](https://github.com/admica/FileScopeMCP)  
**⭐ Stars**: 193 个  
**🍴 Forks**: 15 个  
**📄 License**: GPL-3.0  
**🏷️ 最新版本**: v2.8.0 (2025年6月11日)

## 🤔 什么是 FileScopeMCP？

FileScopeMCP 是一个基于 TypeScript 的 MCP (Model Context Protocol) 服务器，专门用于分析代码库结构和依赖关系。它能够：

- 🎯 **智能分析代码库** - 识别最重要的文件
- 🔗 **依赖关系追踪** - 映射文件间的双向依赖
- 📊 **可视化图表** - 生成 Mermaid 依赖图
- 📝 **文件摘要管理** - 为文件添加和管理摘要
- 🌍 **多语言支持** - 支持 Python、JavaScript、TypeScript、C/C++、Rust、Lua、Zig、C#、Java

## ✨ 核心特性

### 1. 文件重要性分析
- 基于依赖关系计算文件重要性评分 (0-10)
- 考虑文件类型、位置和命名规范
- 识别代码库中的关键文件

### 2. 依赖关系追踪
- 双向依赖关系映射
- 区分本地依赖和包依赖
- 支持多种编程语言的导入语法

### 3. 可视化功能
- 生成 Mermaid 图表
- 基于重要性的颜色编码
- 支持依赖图、目录树或混合视图
- HTML 输出，支持主题切换

### 4. 多项目支持
- 管理多个文件树
- 独立的基础目录配置
- 缓存机制提高性能

## 🚀 安装和配置

### 1. 克隆仓库

```bash
git clone https://github.com/admica/FileScopeMCP.git
cd FileScopeMCP
```

### 2. 构建项目

**Windows:**
```bash
build.bat
```

**Linux/Mac:**
```bash
chmod +x build.sh
./build.sh
```

### 3. 配置 MCP

将生成的配置添加到 Cursor 的 `.cursor/mcp.json` 文件中：

```json
{
  "mcpServers": {
    "FileScopeMCP": {
      "command": "node",
      "args": ["<path-to-build>/mcp-server.js", "--base-dir=/path/to/your/project"],
      "transport": "stdio",
      "disabled": false,
      "alwaysAllow": []
    }
  }
}
```

**Linux WSL 配置示例:**
```json
{
  "mcpServers": {
    "FileScopeMCP": {
      "command": "wsl",
      "args": ["-d", "Ubuntu-24.04", "/home/<USER>/FileScopeMCP/run.sh"],
      "transport": "stdio",
      "disabled": false,
      "alwaysAllow": []
    }
  }
}
```

## 🔧 可用工具

### 文件树管理
- `list_saved_trees` - 列出所有保存的文件树
- `create_file_tree` - 为特定目录创建新的文件树配置
- `select_file_tree` - 选择现有文件树进行工作
- `delete_file_tree` - 删除文件树配置

### 文件分析
- `list_files` - 列出项目中所有文件及其重要性排名
- `get_file_importance` - 获取特定文件的详细信息
- `find_important_files` - 查找项目中最重要的文件
- `read_file_content` - 读取特定文件的内容
- `recalculate_importance` - 重新计算所有文件的重要性值

### 文件摘要
- `get_file_summary` - 获取文件的存储摘要
- `set_file_summary` - 设置或更新文件摘要

### 文件监控
- `toggle_file_watching` - 开启/关闭文件监控
- `get_file_watching_status` - 获取文件监控状态
- `update_file_watching_config` - 更新文件监控配置

### 图表生成
- `generate_diagram` - 创建可自定义的 Mermaid 图表

## 📈 工作原理

### 依赖检测机制

FileScopeMCP 扫描源代码中的导入语句和特定语言模式：

- **Python**: `import` 和 `from ... import` 语句
- **JavaScript/TypeScript**: `import` 语句和 `require()` 调用
- **C/C++**: `#include` 指令
- **Rust**: `use` 和 `mod` 语句
- **Lua**: `require` 语句
- **Zig**: `@import` 指令
- **C#**: `using` 指令
- **Java**: `import` 语句

### 重要性计算算法

文件重要性评分 (0-10) 基于加权公式，考虑以下因素：

1. **依赖者数量** - 导入此文件的文件数量
2. **依赖数量** - 此文件导入的文件数量
3. **文件类型** - TypeScript/JavaScript 文件获得更高基础分数
4. **项目位置** - `src/` 目录中的文件权重更高
5. **文件命名** - 'index'、'main'、'server' 等关键文件获得额外分数

### 图表生成流程

系统使用三阶段方法生成有效的 Mermaid 语法：

1. **收集阶段** - 注册所有节点和关系
2. **节点定义阶段** - 在任何引用之前生成所有节点的定义
3. **边生成阶段** - 在已定义节点之间创建边

## 💡 使用示例

### 1. 分析项目

```javascript
// 创建项目文件树
create_file_tree(filename: "my-project.json", baseDirectory: "/path/to/project")

// 查找最重要的文件
find_important_files(limit: 5, minImportance: 5)

// 获取特定文件的详细信息
get_file_importance(filepath: "/path/to/project/src/main.ts")
```

### 2. 管理文件摘要

```javascript
// 读取文件内容
read_file_content(filepath: "/path/to/project/src/main.ts")

// 添加文件摘要
set_file_summary(
  filepath: "/path/to/project/src/main.ts", 
  summary: "主入口点，初始化应用程序，设置路由并启动服务器。"
)

// 检索摘要
get_file_summary(filepath: "/path/to/project/src/main.ts")
```

### 3. 生成图表

```javascript
// 创建基本项目结构图
generate_diagram(
  style: "directory", 
  maxDepth: 3, 
  outputPath: "diagrams/project-structure", 
  outputFormat: "mmd"
)

// 生成带依赖关系的 HTML 图表
generate_diagram(
  style: "hybrid", 
  maxDepth: 2, 
  minImportance: 5, 
  showDependencies: true, 
  outputPath: "diagrams/important-files", 
  outputFormat: "html"
)
```

### 4. 文件监控

```javascript
// 启用文件监控
toggle_file_watching()

// 检查监控状态
get_file_watching_status()

// 更新监控配置
update_file_watching_config({
  debounceMs: 500,
  autoRebuildTree: true,
  watchForNewFiles: true,
  watchForDeleted: true,
  watchForChanged: true
})
```

## 🎯 最佳实践

### 1. 项目初始化
- 首次使用时让 AI 助手分析并为重要文件创建摘要
- 使用 `set_file_summary` 工具添加文件描述

### 2. 依赖分析
- 定期运行 `recalculate_importance` 更新重要性评分
- 使用 `find_important_files` 识别关键文件

### 3. 可视化
- 为不同用途生成不同类型的图表
- 使用 HTML 格式获得更好的交互体验

### 4. 多项目管理
- 为不同项目区域创建独立的文件树
- 使用描述性的文件名管理多个配置

## 🔄 最新改进

- ✅ 改进的排除逻辑，忽略隐藏的虚拟环境（如 `.venv`）
- ✅ 添加测试框架 (Vitest)
- ✅ 支持更多编程语言
- ✅ 增强的路径规范化处理
- ✅ 改进的图表生成算法

## 🚧 未来计划

- 🔮 更复杂的重要性计算算法
- 🎨 增强图表自定义选项
- 📤 支持导出到更多格式
- 🔍 更智能的代码分析功能

## 📞 支持和社区

- **GitHub Issues**: [报告问题](https://github.com/admica/FileScopeMCP/issues)
- **讨论**: GitHub Discussions
- **贡献**: 欢迎提交 Pull Request

---

💡 **提示**: FileScopeMCP 最适合与 Cursor IDE 和 Claude 等 AI 助手配合使用，能够显著提升代码理解和开发效率！
