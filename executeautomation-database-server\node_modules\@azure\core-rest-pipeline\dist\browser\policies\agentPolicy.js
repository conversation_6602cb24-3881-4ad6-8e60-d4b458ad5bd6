// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { agentPolicyName as tspAgentPolicyName, agentPolicy as tspAgentPolicy, } from "@typespec/ts-http-runtime/internal/policies";
/**
 * Name of the Agent Policy
 */
export const agentPolicyName = tspAgentPolicyName;
/**
 * Gets a pipeline policy that sets http.agent
 */
export function agentPolicy(agent) {
    return tspAgentPolicy(agent);
}
//# sourceMappingURL=agentPolicy.js.map