{"version": 3, "file": "keyCredential.js", "sourceRoot": "", "sources": ["../../src/keyCredential.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAmBlC,0CAEC;AAnBD,gDAA0D;AAY1D;;;;GAIG;AACH,SAAgB,eAAe,CAAC,UAAmB;IACjD,OAAO,IAAA,kCAAsB,EAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAC3F,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isObjectWithProperties } from \"@azure/core-util\";\n\n/**\n * Represents a credential defined by a static API key.\n */\nexport interface KeyCredential {\n  /**\n   * The value of the API key represented as a string\n   */\n  readonly key: string;\n}\n\n/**\n * Tests an object to determine whether it implements KeyCredential.\n *\n * @param credential - The assumed KeyCredential to be tested.\n */\nexport function isKeyCredential(credential: unknown): credential is KeyCredential {\n  return isObjectWithProperties(credential, [\"key\"]) && typeof credential.key === \"string\";\n}\n"]}