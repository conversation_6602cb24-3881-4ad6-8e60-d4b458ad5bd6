{"version": 3, "file": "lroEngine.js", "sourceRoot": "", "sources": ["../../../../src/legacy/lroEngine/lroEngine.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AAEtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAEhE,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAEtC,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7D;;GAEG;AACH,MAAM,OAAO,SAA+D,SAAQ,MAGnF;IAGC,YAAY,GAAkC,EAAE,OAA2C;QACzF,MAAM,EACJ,YAAY,GAAG,mBAAmB,EAClC,UAAU,EACV,qBAAqB,GAAG,KAAK,EAC7B,MAAM,EACN,yBAAyB,EACzB,aAAa,EACb,WAAW,GACZ,GAAG,OAAO,IAAI,EAAE,CAAC;QAClB,MAAM,KAAK,GAAqC,UAAU;YACxD,CAAC,CAAC,gBAAgB,CAAC,UAAU,CAAC;YAC9B,CAAC,CAAE,EAAuC,CAAC;QAC7C,MAAM,SAAS,GAAG,IAAI,oBAAoB,CACxC,KAAK,EACL,GAAG,EACH,CAAC,qBAAqB,EACtB,yBAAyB,EACzB,aAAa,EACb,WAAW,EACX,MAAM,CACP,CAAC;QACF,KAAK,CAAC,SAAS,CAAC,CAAC;QACjB,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QAEnD,IAAI,CAAC,MAAM,GAAG,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;QAC7C,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;IACzF,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { LroEngineOptions, PollerConfig } from \"./models.js\";\nimport { GenericPollOperation } from \"./operation.js\";\nimport { LongRunningOperation } from \"../../http/models.js\";\nimport { POLL_INTERVAL_IN_MS } from \"../../poller/constants.js\";\nimport { PollOperationState } from \"../pollOperation.js\";\nimport { Poller } from \"../poller.js\";\nimport { RestorableOperationState } from \"../../poller/models.js\";\nimport { deserializeState } from \"../../poller/operation.js\";\n\n/**\n * The LRO Engine, a class that performs polling.\n */\nexport class LroEngine<TResult, TState extends PollOperationState<TResult>> extends Poller<\n  TState,\n  TResult\n> {\n  private config: PollerConfig;\n\n  constructor(lro: LongRunningOperation<TResult>, options?: LroEngineOptions<TResult, TState>) {\n    const {\n      intervalInMs = POLL_INTERVAL_IN_MS,\n      resumeFrom,\n      resolveOnUnsuccessful = false,\n      isDone,\n      lroResourceLocationConfig,\n      processResult,\n      updateState,\n    } = options || {};\n    const state: RestorableOperationState<TState> = resumeFrom\n      ? deserializeState(resumeFrom)\n      : ({} as RestorableOperationState<TState>);\n    const operation = new GenericPollOperation(\n      state,\n      lro,\n      !resolveOnUnsuccessful,\n      lroResourceLocationConfig,\n      processResult,\n      updateState,\n      isDone,\n    );\n    super(operation);\n    this.resolveOnUnsuccessful = resolveOnUnsuccessful;\n\n    this.config = { intervalInMs: intervalInMs };\n    operation.setPollerConfig(this.config);\n  }\n\n  /**\n   * The method used by the poller to wait before attempting to update its operation.\n   */\n  delay(): Promise<void> {\n    return new Promise((resolve) => setTimeout(() => resolve(), this.config.intervalInMs));\n  }\n}\n"]}