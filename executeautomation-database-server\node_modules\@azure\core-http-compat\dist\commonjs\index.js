"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.toHttpHeadersLike = exports.convertHttpClient = exports.disableKeepAlivePolicyName = exports.HttpPipelineLogLevel = exports.createRequestPolicyFactoryPolicy = exports.requestPolicyFactoryPolicyName = exports.ExtendedServiceClient = void 0;
/**
 * A Shim Library that provides compatibility between Core V1 & V2 Packages.
 *
 * @packageDocumentation
 */
var extendedClient_js_1 = require("./extendedClient.js");
Object.defineProperty(exports, "ExtendedServiceClient", { enumerable: true, get: function () { return extendedClient_js_1.ExtendedServiceClient; } });
var requestPolicyFactoryPolicy_js_1 = require("./policies/requestPolicyFactoryPolicy.js");
Object.defineProperty(exports, "requestPolicyFactoryPolicyName", { enumerable: true, get: function () { return requestPolicyFactoryPolicy_js_1.requestPolicyFactoryPolicyName; } });
Object.defineProperty(exports, "createRequestPolicyFactoryPolicy", { enumerable: true, get: function () { return requestPolicyFactoryPolicy_js_1.createRequestPolicyFactoryPolicy; } });
Object.defineProperty(exports, "HttpPipelineLogLevel", { enumerable: true, get: function () { return requestPolicyFactoryPolicy_js_1.HttpPipelineLogLevel; } });
var disableKeepAlivePolicy_js_1 = require("./policies/disableKeepAlivePolicy.js");
Object.defineProperty(exports, "disableKeepAlivePolicyName", { enumerable: true, get: function () { return disableKeepAlivePolicy_js_1.disableKeepAlivePolicyName; } });
var httpClientAdapter_js_1 = require("./httpClientAdapter.js");
Object.defineProperty(exports, "convertHttpClient", { enumerable: true, get: function () { return httpClientAdapter_js_1.convertHttpClient; } });
var util_js_1 = require("./util.js");
Object.defineProperty(exports, "toHttpHeadersLike", { enumerable: true, get: function () { return util_js_1.toHttpHeadersLike; } });
//# sourceMappingURL=index.js.map