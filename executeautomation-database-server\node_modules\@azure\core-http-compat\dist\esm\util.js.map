{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,MAAM,2BAA2B,CAAC;AAIrF,8EAA8E;AAC9E,uCAAuC;AACvC,MAAM,qBAAqB,GAAG,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAEjE,wEAAwE;AACxE,qFAAqF;AACrF,mFAAmF;AACnF,oBAAoB;AACpB,MAAM,2BAA2B,GAAG,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AAKtF,MAAM,UAAU,iBAAiB,CAC/B,WAA4B,EAC5B,UAEI,EAAE;IAEN,MAAM,iBAAiB,GAAG,WAAoC,CAAC;IAC/D,MAAM,OAAO,GAAG,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;IACzD,MAAM,OAAO,GAAG,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACtF,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,OAAO,CAAC;IACjB,CAAC;SAAM,CAAC;QACN,MAAM,UAAU,GAAG,qBAAqB,CAAC;YACvC,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,OAAO;YACP,eAAe,EAAE,WAAW,CAAC,eAAe;YAC5C,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,gBAAgB,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS;YACzC,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;YAClD,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;YAC9C,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,yBAAyB,EAAE,WAAW,CAAC,yBAAyB;YAChE,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;SAC/C,CAAC,CAAC;QACH,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC3B,UAA0C,CAAC,2BAA2B,CAAC;gBACtE,OAAO,CAAC,eAAe,CAAC;QAC5B,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,OAAwB,EACxB,OAAsE;;IAEtE,MAAM,eAAe,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,mCAAI,OAAO,CAAC;IAC5D,MAAM,WAAW,GAAoB;QACnC,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,OAAO,EAAE,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC;QAC3C,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,OAAO,CAAC,SAAS;QAC7E,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB;QACrC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;QAC9C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;QAC1C,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;QAC5D,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;QAC1C,KAAK;YACH,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QACD,OAAO;YACL,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;QAC3F,CAAC;QACD,yBAAyB;YACvB,iBAAiB;QACnB,CAAC;KACF,CAAC;IAEF,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,CAAC;QACzB,OAAO,IAAI,KAAK,CAAC,WAAW,EAAE;YAC5B,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ;gBACxB,IAAI,IAAI,KAAK,qBAAqB,EAAE,CAAC;oBACnC,OAAO,OAAO,CAAC;gBACjB,CAAC;qBAAM,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC5B,OAAO,GAAG,EAAE;wBACV,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;4BAC5E,WAAW,EAAE,IAAI;4BACjB,eAAe;yBAChB,CAAC,CAAC;oBACL,CAAC,CAAC;gBACJ,CAAC;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC7C,CAAC;YACD,GAAG,CAAC,MAAW,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ;gBACpC,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;oBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,KAAK,CAAC;gBACpC,CAAC;gBACD,MAAM,gBAAgB,GAAG;oBACvB,KAAK;oBACL,QAAQ;oBACR,iBAAiB;oBACjB,SAAS;oBACT,WAAW;oBACX,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,oBAAoB;oBACpB,kBAAkB;oBAClB,eAAe;oBACf,2BAA2B;oBAC3B,OAAO;oBACP,kBAAkB;iBACnB,CAAC;gBAEF,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/D,OAAe,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBACjC,CAAC;gBAED,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACpD,CAAC;SACF,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,OAAO,WAAW,CAAC;IACrB,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,OAAsB;IACtD,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACjE,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,UAAkB;IACtC,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;AA4ED;;GAEG;AACH,MAAM,OAAO,WAAW;IAGtB,YAAY,UAA2B;QACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE,CAAC;gBACpC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,UAAkB,EAAE,WAA4B;QACzD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG;YAC3C,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;SAC9B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,UAAkB;QAC3B,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,UAAkB;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,UAAkB;QAC9B,MAAM,MAAM,GAAY,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACxC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACxC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAsC,EAAE;QACpD,MAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACzC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACvD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;YACrC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACzC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACvD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;YACnD,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,KAAK;QACV,MAAM,sBAAsB,GAAmB,EAAE,CAAC;QAClD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,WAAW,CAAC,sBAAsB,CAAC,CAAC;IACjD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpMethods, ProxySettings } from \"@azure/core-rest-pipeline\";\nimport { createHttpHeaders, createPipelineRequest } from \"@azure/core-rest-pipeline\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { HttpHeaders as HttpHeadersV2, PipelineRequest } from \"@azure/core-rest-pipeline\";\n\n// We use a custom symbol to cache a reference to the original request without\n// exposing it on the public interface.\nconst originalRequestSymbol = Symbol(\"Original PipelineRequest\");\ntype CompatWebResourceLike = WebResourceLike & { [originalRequestSymbol]?: PipelineRequest };\n// Symbol.for() will return the same symbol if it's already been created\n// This particular one is used in core-client to handle the case of when a request is\n// cloned but we need to retrieve the OperationSpec and OperationArguments from the\n// original request.\nconst originalClientRequestSymbol = Symbol.for(\"@azure/core-client original request\");\ntype PipelineRequestWithOriginal = PipelineRequest & {\n  [originalClientRequestSymbol]?: PipelineRequest;\n};\n\nexport function toPipelineRequest(\n  webResource: WebResourceLike,\n  options: {\n    originalRequest?: PipelineRequest;\n  } = {},\n): PipelineRequest {\n  const compatWebResource = webResource as CompatWebResourceLike;\n  const request = compatWebResource[originalRequestSymbol];\n  const headers = createHttpHeaders(webResource.headers.toJson({ preserveCase: true }));\n  if (request) {\n    request.headers = headers;\n    return request;\n  } else {\n    const newRequest = createPipelineRequest({\n      url: webResource.url,\n      method: webResource.method,\n      headers,\n      withCredentials: webResource.withCredentials,\n      timeout: webResource.timeout,\n      requestId: webResource.requestId,\n      abortSignal: webResource.abortSignal,\n      body: webResource.body,\n      formData: webResource.formData,\n      disableKeepAlive: !!webResource.keepAlive,\n      onDownloadProgress: webResource.onDownloadProgress,\n      onUploadProgress: webResource.onUploadProgress,\n      proxySettings: webResource.proxySettings,\n      streamResponseStatusCodes: webResource.streamResponseStatusCodes,\n      agent: webResource.agent,\n      requestOverrides: webResource.requestOverrides,\n    });\n    if (options.originalRequest) {\n      (newRequest as PipelineRequestWithOriginal)[originalClientRequestSymbol] =\n        options.originalRequest;\n    }\n    return newRequest;\n  }\n}\n\nexport function toWebResourceLike(\n  request: PipelineRequest,\n  options?: { createProxy?: boolean; originalRequest?: PipelineRequest },\n): WebResourceLike {\n  const originalRequest = options?.originalRequest ?? request;\n  const webResource: WebResourceLike = {\n    url: request.url,\n    method: request.method,\n    headers: toHttpHeadersLike(request.headers),\n    withCredentials: request.withCredentials,\n    timeout: request.timeout,\n    requestId: request.headers.get(\"x-ms-client-request-id\") || request.requestId,\n    abortSignal: request.abortSignal,\n    body: request.body,\n    formData: request.formData,\n    keepAlive: !!request.disableKeepAlive,\n    onDownloadProgress: request.onDownloadProgress,\n    onUploadProgress: request.onUploadProgress,\n    proxySettings: request.proxySettings,\n    streamResponseStatusCodes: request.streamResponseStatusCodes,\n    agent: request.agent,\n    requestOverrides: request.requestOverrides,\n    clone(): WebResourceLike {\n      throw new Error(\"Cannot clone a non-proxied WebResourceLike\");\n    },\n    prepare(): WebResourceLike {\n      throw new Error(\"WebResourceLike.prepare() is not supported by @azure/core-http-compat\");\n    },\n    validateRequestProperties(): void {\n      /** do nothing */\n    },\n  };\n\n  if (options?.createProxy) {\n    return new Proxy(webResource, {\n      get(target, prop, receiver) {\n        if (prop === originalRequestSymbol) {\n          return request;\n        } else if (prop === \"clone\") {\n          return () => {\n            return toWebResourceLike(toPipelineRequest(webResource, { originalRequest }), {\n              createProxy: true,\n              originalRequest,\n            });\n          };\n        }\n        return Reflect.get(target, prop, receiver);\n      },\n      set(target: any, prop, value, receiver) {\n        if (prop === \"keepAlive\") {\n          request.disableKeepAlive = !value;\n        }\n        const passThroughProps = [\n          \"url\",\n          \"method\",\n          \"withCredentials\",\n          \"timeout\",\n          \"requestId\",\n          \"abortSignal\",\n          \"body\",\n          \"formData\",\n          \"onDownloadProgress\",\n          \"onUploadProgress\",\n          \"proxySettings\",\n          \"streamResponseStatusCodes\",\n          \"agent\",\n          \"requestOverrides\",\n        ];\n\n        if (typeof prop === \"string\" && passThroughProps.includes(prop)) {\n          (request as any)[prop] = value;\n        }\n\n        return Reflect.set(target, prop, value, receiver);\n      },\n    });\n  } else {\n    return webResource;\n  }\n}\n\n/**\n * Converts HttpHeaders from core-rest-pipeline to look like\n * HttpHeaders from core-http.\n * @param headers - HttpHeaders from core-rest-pipeline\n * @returns HttpHeaders as they looked in core-http\n */\nexport function toHttpHeadersLike(headers: HttpHeadersV2): HttpHeadersLike {\n  return new HttpHeaders(headers.toJSON({ preserveCase: true }));\n}\n\n/**\n * A collection of HttpHeaders that can be sent with a HTTP request.\n */\nfunction getHeaderKey(headerName: string): string {\n  return headerName.toLowerCase();\n}\n\n/**\n * An individual header within a HttpHeaders collection.\n */\nexport interface HttpHeader {\n  /**\n   * The name of the header.\n   */\n  name: string;\n\n  /**\n   * The value of the header.\n   */\n  value: string;\n}\n\n/**\n * A HttpHeaders collection represented as a simple JSON object.\n */\nexport type RawHttpHeaders = { [headerName: string]: string };\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport interface HttpHeadersLike {\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  set(headerName: string, headerValue: string | number): void;\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  get(headerName: string): string | undefined;\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  contains(headerName: string): boolean;\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  remove(headerName: string): boolean;\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  rawHeaders(): RawHttpHeaders;\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  headersArray(): HttpHeader[];\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  headerNames(): string[];\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  headerValues(): string[];\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  clone(): HttpHeadersLike;\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   * The result is the same as `rawHeaders()`.\n   */\n  toJson(options?: { preserveCase?: boolean }): RawHttpHeaders;\n}\n\n/**\n * A collection of HTTP header key/value pairs.\n */\nexport class HttpHeaders implements HttpHeadersLike {\n  private readonly _headersMap: { [headerKey: string]: HttpHeader };\n\n  constructor(rawHeaders?: RawHttpHeaders) {\n    this._headersMap = {};\n    if (rawHeaders) {\n      for (const headerName in rawHeaders) {\n        this.set(headerName, rawHeaders[headerName]);\n      }\n    }\n  }\n\n  /**\n   * Set a header in this collection with the provided name and value. The name is\n   * case-insensitive.\n   * @param headerName - The name of the header to set. This value is case-insensitive.\n   * @param headerValue - The value of the header to set.\n   */\n  public set(headerName: string, headerValue: string | number): void {\n    this._headersMap[getHeaderKey(headerName)] = {\n      name: headerName,\n      value: headerValue.toString(),\n    };\n  }\n\n  /**\n   * Get the header value for the provided header name, or undefined if no header exists in this\n   * collection with the provided name.\n   * @param headerName - The name of the header.\n   */\n  public get(headerName: string): string | undefined {\n    const header: HttpHeader = this._headersMap[getHeaderKey(headerName)];\n    return !header ? undefined : header.value;\n  }\n\n  /**\n   * Get whether or not this header collection contains a header entry for the provided header name.\n   */\n  public contains(headerName: string): boolean {\n    return !!this._headersMap[getHeaderKey(headerName)];\n  }\n\n  /**\n   * Remove the header with the provided headerName. Return whether or not the header existed and\n   * was removed.\n   * @param headerName - The name of the header to remove.\n   */\n  public remove(headerName: string): boolean {\n    const result: boolean = this.contains(headerName);\n    delete this._headersMap[getHeaderKey(headerName)];\n    return result;\n  }\n\n  /**\n   * Get the headers that are contained this collection as an object.\n   */\n  public rawHeaders(): RawHttpHeaders {\n    return this.toJson({ preserveCase: true });\n  }\n\n  /**\n   * Get the headers that are contained in this collection as an array.\n   */\n  public headersArray(): HttpHeader[] {\n    const headers: HttpHeader[] = [];\n    for (const headerKey in this._headersMap) {\n      headers.push(this._headersMap[headerKey]);\n    }\n    return headers;\n  }\n\n  /**\n   * Get the header names that are contained in this collection.\n   */\n  public headerNames(): string[] {\n    const headerNames: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerNames.push(headers[i].name);\n    }\n    return headerNames;\n  }\n\n  /**\n   * Get the header values that are contained in this collection.\n   */\n  public headerValues(): string[] {\n    const headerValues: string[] = [];\n    const headers: HttpHeader[] = this.headersArray();\n    for (let i = 0; i < headers.length; ++i) {\n      headerValues.push(headers[i].value);\n    }\n    return headerValues;\n  }\n\n  /**\n   * Get the JSON object representation of this HTTP header collection.\n   */\n  public toJson(options: { preserveCase?: boolean } = {}): RawHttpHeaders {\n    const result: RawHttpHeaders = {};\n    if (options.preserveCase) {\n      for (const headerKey in this._headersMap) {\n        const header: HttpHeader = this._headersMap[headerKey];\n        result[header.name] = header.value;\n      }\n    } else {\n      for (const headerKey in this._headersMap) {\n        const header: HttpHeader = this._headersMap[headerKey];\n        result[getHeaderKey(header.name)] = header.value;\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Get the string representation of this HTTP header collection.\n   */\n  public toString(): string {\n    return JSON.stringify(this.toJson({ preserveCase: true }));\n  }\n\n  /**\n   * Create a deep clone/copy of this HttpHeaders collection.\n   */\n  public clone(): HttpHeaders {\n    const resultPreservingCasing: RawHttpHeaders = {};\n    for (const headerKey in this._headersMap) {\n      const header: HttpHeader = this._headersMap[headerKey];\n      resultPreservingCasing[header.name] = header.value;\n    }\n    return new HttpHeaders(resultPreservingCasing);\n  }\n}\n\n/**\n * An interface compatible with NodeJS's `http.Agent`.\n * We want to avoid publicly re-exporting the actual interface,\n * since it might vary across runtime versions.\n */\nexport interface Agent {\n  /**\n   * Destroy any sockets that are currently in use by the agent.\n   */\n  destroy(): void;\n  /**\n   * For agents with keepAlive enabled, this sets the maximum number of sockets that will be left open in the free state.\n   */\n  maxFreeSockets: number;\n  /**\n   * Determines how many concurrent sockets the agent can have open per origin.\n   */\n  maxSockets: number;\n  /**\n   * An object which contains queues of requests that have not yet been assigned to sockets.\n   */\n  requests: unknown;\n  /**\n   * An object which contains arrays of sockets currently in use by the agent.\n   */\n  sockets: unknown;\n}\n\n/**\n * A description of a HTTP request to be made to a remote server.\n */\nexport interface WebResourceLike {\n  /**\n   * The URL being accessed by the request.\n   */\n  url: string;\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method: HttpMethods;\n  /**\n   * The HTTP body contents of the request.\n   */\n  body?: any;\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * Whether or not the body of the HttpOperationResponse should be treated as a stream.\n   * @deprecated Use streamResponseStatusCodes property instead.\n   */\n  streamResponseBody?: boolean;\n  /**\n   * A list of response status codes whose corresponding HttpOperationResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n  /**\n   * Form data, used to build the request body.\n   */\n  formData?: any;\n  /**\n   * A query string represented as an object.\n   */\n  query?: { [key: string]: any };\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   */\n  withCredentials: boolean;\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   */\n  timeout: number;\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n  /**\n   * If the connection should be reused.\n   */\n  keepAlive?: boolean;\n  /**\n   * Whether or not to decompress response according to Accept-Encoding header (node-fetch only)\n   */\n  decompressResponse?: boolean;\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId: string;\n\n  /**\n   * Signal of an abort controller. Can be used to abort both sending a network request and waiting for a response.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * NODEJS ONLY\n   *\n   * A Node-only option to provide a custom `http.Agent`/`https.Agent`.\n   * NOTE: usually this should be one instance shared by multiple requests so that the underlying\n   *       connection to the service can be reused.\n   * Does nothing when running in the browser.\n   */\n  agent?: Agent;\n\n  /**\n   * Additional options to set on the request. This provides a way to override\n   * existing ones or provide request properties that are not declared.\n   *\n   * For possible valid properties, see\n   *   - NodeJS https.request options:  https://nodejs.org/api/http.html#httprequestoptions-callback\n   *   - Browser RequestInit: https://developer.mozilla.org/en-US/docs/Web/API/RequestInit\n   *\n   * WARNING: Options specified here will override any properties of same names when request is sent by {@link HttpClient}.\n   */\n  requestOverrides?: Record<string, unknown>;\n\n  /**\n   * Clone this request object.\n   */\n  clone(): WebResourceLike;\n\n  /**\n   * Validates that the required properties such as method, url, headers[\"Content-Type\"],\n   * headers[\"accept-language\"] are defined. It will throw an error if one of the above\n   * mentioned properties are not defined.\n   * Note: this a no-op for compat purposes.\n   */\n  validateRequestProperties(): void;\n\n  /**\n   * This is a no-op for compat purposes and will throw if called.\n   */\n  prepare(options: unknown): WebResourceLike;\n}\n\n/**\n * Fired in response to upload or download progress.\n */\nexport type TransferProgressEvent = {\n  /**\n   * The number of bytes loaded so far.\n   */\n  loadedBytes: number;\n};\n"]}