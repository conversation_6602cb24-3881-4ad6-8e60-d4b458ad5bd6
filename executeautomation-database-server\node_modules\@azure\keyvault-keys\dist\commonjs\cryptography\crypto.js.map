{"version": 3, "file": "crypto.js", "sourceRoot": "", "sources": ["../../../src/cryptography/crypto.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AA8BlC,gCAaC;AAMD,oCAaC;AAMD,kCAEC;AAnED,6CAIqB;AAErB;;;IAGI;AACJ,MAAM,wBAAwB,GAA4B;IACxD,KAAK,EAAE,QAAQ;IACf,MAAM,EAAE,QAAQ;IAChB,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;CAChB,CAAC;AAEF;;;GAGG;AACI,KAAK,UAAU,UAAU,CAAC,SAAiB,EAAE,IAAgB;IAClE,MAAM,aAAa,GAAG,wBAAwB,CAAC,SAAS,CAAC,CAAC;IAC1D,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CACb,qBAAqB,SAAS,gDAAgD,MAAM,CAAC,IAAI,CACvF,wBAAwB,CACzB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACf,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,GAAG,IAAA,wBAAgB,EAAC,aAAa,CAAC,CAAC;IAC7C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC7B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAgB,YAAY,CAAC,SAAiB,EAAE,IAAgB;IAC9D,MAAM,eAAe,GAAG,wBAAwB,CAAC,SAAS,CAAC,CAAC;IAC5D,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,MAAM,IAAI,KAAK,CACb,qBAAqB,SAAS,gDAAgD,MAAM,CAAC,IAAI,CACvF,wBAAwB,CACzB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACf,CAAC;IACJ,CAAC;IACD,MAAM,QAAQ,GAAG,IAAA,0BAAkB,EAAC,eAAe,CAAC,CAAC;IACrD,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACnC,QAAQ,CAAC,GAAG,EAAE,CAAC;IACf,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;GAGG;AACH,SAAgB,WAAW,CAAC,MAAc;IACxC,OAAO,IAAA,yBAAiB,EAAC,MAAM,CAAC,CAAC;AACnC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { Verify } from \"node:crypto\";\nimport {\n  createHash as cryptoCreateHash,\n  createVerify as cryptoCreateVerify,\n  randomBytes as cryptoRandomBytes,\n} from \"node:crypto\";\n\n/**\n * @internal\n * Mapping between signature algorithms and their corresponding hash algorithms. Externally used for testing.\n **/\nconst algorithmToHashAlgorithm: { [s: string]: string } = {\n  ES256: \"SHA256\",\n  ES256K: \"SHA256\",\n  PS256: \"SHA256\",\n  RS256: \"SHA256\",\n  ES384: \"SHA384\",\n  PS384: \"SHA384\",\n  RS384: \"SHA384\",\n  ES512: \"SHA512\",\n  PS512: \"SHA512\",\n  RS512: \"SHA512\",\n};\n\n/**\n * @internal\n * Use the platform-local hashing functionality\n */\nexport async function createHash(algorithm: string, data: Uint8Array): Promise<Buffer> {\n  const hashAlgorithm = algorithmToHashAlgorithm[algorithm];\n  if (!hashAlgorithm) {\n    throw new Error(\n      `Invalid algorithm ${algorithm} passed to createHash. Supported algorithms: ${Object.keys(\n        algorithmToHashAlgorithm,\n      ).join(\", \")}`,\n    );\n  }\n  const hash = cryptoCreateHash(hashAlgorithm);\n  hash.update(Buffer.from(data));\n  const digest = hash.digest();\n  return digest;\n}\n\n/**\n * @internal\n * Use the platform-local verify functionality\n */\nexport function createVerify(algorithm: string, data: Uint8Array): Verify {\n  const verifyAlgorithm = algorithmToHashAlgorithm[algorithm];\n  if (!verifyAlgorithm) {\n    throw new Error(\n      `Invalid algorithm ${algorithm} passed to createHash. Supported algorithms: ${Object.keys(\n        algorithmToHashAlgorithm,\n      ).join(\", \")}`,\n    );\n  }\n  const verifier = cryptoCreateVerify(verifyAlgorithm);\n  verifier.update(Buffer.from(data));\n  verifier.end();\n  return verifier;\n}\n\n/**\n * @internal\n * Use the platform-local randomBytes functionality\n */\nexport function randomBytes(length: number): Uint8Array {\n  return cryptoRandomBytes(length);\n}\n"]}