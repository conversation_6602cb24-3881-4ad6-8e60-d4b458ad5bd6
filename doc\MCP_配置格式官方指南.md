# MCP 配置格式官方指南

## Augment Code MCP 配置格式

根据 Augment 官方文档 (https://docs.augmentcode.com/setup-augment/mcp)，有两种配置 MCP 服务器的方式：

### 1. 使用 Augment 设置面板配置
- 点击 Augment 面板右上角的齿轮图标
- 在 MCP 服务器部分填写 `name` 和 `command` 字段
- `name` 必须是服务器的唯一名称
- `command` 是运行服务器的命令，包括参数和环境变量

### 2. 在 settings.json 中配置

**配置路径**：
1. 按 Cmd/Ctrl Shift P 或点击 Augment 面板的汉堡菜单
2. 选择 Edit Settings
3. 在 Advanced 下，点击 Edit in settings.json

**配置格式**：
```json
{
    "augment.advanced": {
        "mcpServers": [
            {
                "name": "sqlite",
                "command": "uvx",
                "args": ["mcp-server-sqlite", "--db-path", "/path/to/test.db"]
            }
        ]
    }
}
```

## VS Code MCP 配置格式

根据 VS Code 官方文档 (https://code.visualstudio.com/docs/copilot/chat/mcp-servers)：

### 配置文件位置
- **工作区配置**：`.vscode/mcp.json`
- **用户配置**：通过 `MCP: Open User Configuration` 命令打开

### 配置格式

```json
{
  "inputs": [
    {
      "type": "promptString",
      "id": "perplexity-key",
      "description": "Perplexity API Key",
      "password": true
    }
  ],
  "servers": {
    "Perplexity": {
      "type": "stdio",
      "command": "docker",
      "args": ["run", "-i", "--rm", "-e", "PERPLEXITY_API_KEY", "mcp/perplexity-ask"],
      "env": {
        "PERPLEXITY_API_KEY": "${input:perplexity-key}"
      }
    },
    "Github": {
      "url": "https://api.githubcopilot.com/mcp/"
    },
    "fetch": {
      "type": "stdio",
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    }
  }
}
```

### 服务器配置字段

#### stdio 类型服务器
- `type`: `"stdio"`
- `command`: 启动服务器的命令
- `args`: 传递给命令的参数数组
- `env`: 服务器的环境变量
- `envFile`: 加载环境变量的 .env 文件路径

#### sse/http 类型服务器
- `type`: `"sse"` 或 `"http"`
- `url`: 服务器的 URL
- `headers`: HTTP 头部信息

### 输入变量 (inputs)
用于定义配置值的占位符，避免硬编码敏感信息：
- `type`: `"promptString"`
- `id`: 唯一标识符
- `description`: 描述信息
- `password`: 是否隐藏输入值

## 关键差异

1. **Augment**: 使用 `augment.advanced.mcpServers` 数组格式
2. **VS Code**: 使用独立的 `mcp.json` 文件格式，支持 `inputs` 和 `servers` 对象

## 最佳实践

1. 避免在配置中硬编码 API 密钥
2. 使用输入变量或环境文件管理敏感信息
3. 为服务器使用描述性的唯一名称
4. 验证命令和参数的正确性
5. 配置完成后重启编辑器
