export { createK<PERSON>Vault, KeyVaultContext, KeyVaultClientOptionalParams, } from "./keyVaultContext.js";
export { getKeyAttestation, getRandomBytes, updateKeyRotationPolicy, getKeyRotationPolicy, recoverDeletedKey, purgeDeletedKey, getDeletedKey, getDeletedKeys, release, unwrapKey, wrapKey, verify, sign, decrypt, encrypt, restoreKey, backupKey, getKeys, getKeyVersions, getKey, updateKey, deleteKey, importKey, rotateKey, createKey, } from "./operations.js";
export { GetKeyAttestationOptionalParams, GetRandomBytesOptionalParams, UpdateKeyRotationPolicyOptionalParams, GetKeyRotationPolicyOptionalParams, RecoverDeletedKeyOptionalParams, PurgeDeletedKeyOptionalParams, GetDeletedKeyOptionalParams, GetDeletedKeysOptionalParams, ReleaseOptionalParams, UnwrapKeyOptionalParams, WrapKeyOptionalParams, VerifyOptionalParams, SignOptionalParams, DecryptOptionalParams, EncryptOptionalParams, RestoreKeyOptionalParams, BackupKeyOptionalParams, GetKeysOptionalParams, GetKeyVersionsOptionalParams, GetKeyOptionalParams, UpdateKeyOptionalParams, DeleteKeyOptionalParams, ImportKeyOptionalParams, RotateKeyOptionalParams, CreateKeyOptionalParams, } from "./options.js";
//# sourceMappingURL=index.d.ts.map