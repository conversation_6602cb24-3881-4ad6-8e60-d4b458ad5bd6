{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../src/http/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { LroError } from \"../poller/models.js\";\n\n// TODO: rename to ResourceLocationConfig\n/**\n * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n */\nexport type LroResourceLocationConfig = \"azure-async-operation\" | \"location\" | \"original-uri\";\n\n/**\n * The type of a LRO response body. This is just a convenience type for checking the status of the operation.\n */\n\nexport interface ResponseBody extends Record<string, unknown> {\n  /** The status of the operation. */\n  status?: unknown;\n  /** The state of the provisioning process */\n  provisioningState?: unknown;\n  /** The properties of the provisioning process */\n  properties?: { provisioningState?: unknown } & Record<string, unknown>;\n  /** The error if the operation failed */\n  error?: Partial<LroError>;\n  /** The location of the created resource */\n  resourceLocation?: string;\n}\n\n/**\n * Simple type of the raw response.\n */\nexport interface RawResponse {\n  /** The HTTP status code */\n  statusCode: number;\n  /** A HttpHeaders collection in the response represented as a simple JSON object where all header names have been normalized to be lower-case. */\n  headers: {\n    [headerName: string]: string;\n  };\n  /** The parsed response body */\n  body?: unknown;\n}\n\n// TODO: rename to OperationResponse\n/**\n * The type of the response of a LRO.\n */\nexport interface LroResponse<T = unknown> {\n  /** The flattened response */\n  flatResponse: T;\n  /** The raw response */\n  rawResponse: RawResponse;\n}\n\n/**\n * Description of a long running operation.\n */\nexport interface LongRunningOperation<T = unknown> {\n  /**\n   * The request path. This should be set if the operation is a PUT and needs\n   * to poll from the same request path.\n   */\n  requestPath?: string;\n  /**\n   * The HTTP request method. This should be set if the operation is a PUT or a\n   * DELETE.\n   */\n  requestMethod?: string;\n  /**\n   * A function that can be used to send initial request to the service.\n   */\n  sendInitialRequest: () => Promise<LroResponse<unknown>>;\n  /**\n   * A function that can be used to poll for the current status of a long running operation.\n   */\n  sendPollRequest: (\n    path: string,\n    options?: { abortSignal?: AbortSignalLike },\n  ) => Promise<LroResponse<T>>;\n}\n\nexport type HttpOperationMode = \"OperationLocation\" | \"ResourceLocation\" | \"Body\";\n\n/**\n * Options for `createPoller`.\n */\nexport interface CreateHttpPollerOptions<TResult, TState> {\n  /**\n   * Defines how much time the poller is going to wait before making a new request to the service.\n   */\n  intervalInMs?: number;\n  /**\n   * A serialized poller which can be used to resume an existing paused Long-Running-Operation.\n   */\n  restoreFrom?: string;\n  /**\n   * The potential location of the result of the LRO if specified by the LRO extension in the swagger.\n   */\n  resourceLocationConfig?: LroResourceLocationConfig;\n  /**\n   * A function to process the result of the LRO.\n   */\n  processResult?: (result: unknown, state: TState) => TResult;\n  /**\n   * A function to process the state of the LRO.\n   */\n  updateState?: (state: TState, response: LroResponse) => void;\n  /**\n   * A function to be called each time the operation location is updated by the\n   * service.\n   */\n  withOperationLocation?: (operationLocation: string) => void;\n  /**\n   * Control whether to throw an exception if the operation failed or was canceled.\n   */\n  resolveOnUnsuccessful?: boolean;\n}\n"]}