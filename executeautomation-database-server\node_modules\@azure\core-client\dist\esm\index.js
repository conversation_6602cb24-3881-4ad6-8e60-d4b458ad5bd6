// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
export { createSerializer, MapperTypeNames } from "./serializer.js";
export { ServiceClient } from "./serviceClient.js";
export { createClientPipeline } from "./pipeline.js";
export { XML_ATTRKEY, XML_CHARKEY, } from "./interfaces.js";
export { deserializationPolicy, deserializationPolicyName, } from "./deserializationPolicy.js";
export { serializationPolicy, serializationPolicyName, } from "./serializationPolicy.js";
export { authorizeRequestOnClaimChallenge } from "./authorizeRequestOnClaimChallenge.js";
export { authorizeRequestOnTenantChallenge } from "./authorizeRequestOnTenantChallenge.js";
//# sourceMappingURL=index.js.map