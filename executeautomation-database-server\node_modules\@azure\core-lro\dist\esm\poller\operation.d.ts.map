{"version": 3, "file": "operation.d.ts", "sourceRoot": "", "sources": ["../../../src/poller/operation.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,QAAQ,EAER,SAAS,EACT,eAAe,EACf,wBAAwB,EACxB,UAAU,EACX,MAAM,aAAa,CAAC;AAIrB;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,MAAM,EACrC,eAAe,EAAE,MAAM,GACtB,wBAAwB,CAAC,MAAM,CAAC,CAMlC;AAuGD;;GAEG;AACH,wBAAsB,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;IACtE,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;IAC5C,UAAU,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACxC,kBAAkB,EAAE,CAAC,MAAM,EAAE;QAC3B,QAAQ,EAAE,SAAS,CAAC;QACpB,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;QACxC,iBAAiB,CAAC,EAAE,MAAM,CAAC;KAC5B,KAAK,eAAe,CAAC;IACtB,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC;IAC9D,qBAAqB,CAAC,EAAE,CAAC,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC;IAChF,gBAAgB,EAAE,OAAO,CAAC;CAC3B,GAAG,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAqB5C;AA4DD,wCAAwC;AACxC,wBAAsB,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE;IAChF,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;IAC7C,UAAU,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACxC,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,CAAC;IACxC,kBAAkB,EAAE,CAClB,QAAQ,EAAE,SAAS,EACnB,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,KACpC,eAAe,CAAC;IACrB,mBAAmB,EAAE,CACnB,QAAQ,EAAE,SAAS,EACnB,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,KACpC,MAAM,GAAG,SAAS,CAAC;IACxB,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC;IAC5C,kBAAkB,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,KAAK,MAAM,GAAG,SAAS,CAAC;IACjE,QAAQ,EAAE,CAAC,YAAY,EAAE,MAAM,KAAK,IAAI,CAAC;IACzC,oBAAoB,CAAC,EAAE,CACrB,QAAQ,EAAE,SAAS,EACnB,KAAK,EAAE,wBAAwB,CAAC,MAAM,CAAC,KACpC,MAAM,GAAG,SAAS,CAAC;IACxB,qBAAqB,CAAC,EAAE,CAAC,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,CAAC;IAChF,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC;IAC9D,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,KAAK,QAAQ,GAAG,SAAS,CAAC;IACzD,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,KAAK,IAAI,CAAC;IAC/D,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC;IAC7D,gBAAgB,EAAE,OAAO,CAAC;IAC1B,OAAO,CAAC,EAAE,QAAQ,CAAC;CACpB,GAAG,OAAO,CAAC,IAAI,CAAC,CAsDhB"}