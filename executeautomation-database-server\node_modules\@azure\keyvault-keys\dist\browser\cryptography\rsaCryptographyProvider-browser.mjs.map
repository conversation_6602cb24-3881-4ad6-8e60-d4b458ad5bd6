{"version": 3, "file": "rsaCryptographyProvider-browser.mjs", "sourceRoot": "", "sources": ["../../../src/cryptography/rsaCryptographyProvider-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,iCAAiC,EAAE,MAAM,aAAa,CAAC;AAEhE;;;;;GAKG;AACH,MAAM,OAAO,uBAAuB;IAClC,OAAO;QACL,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IACD,OAAO;QACL,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO;QACL,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAED,SAAS;QACP,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAED,IAAI;QACF,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAED,QAAQ;QACN,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;IAED,UAAU;QACR,MAAM,IAAI,iCAAiC,CACzC,yDAAyD,CAC1D,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { CryptographyProvider} from \"./models.js\";\nimport { LocalCryptographyUnsupportedError } from \"./models.js\";\n\n/**\n * The browser replacement of the RsaCryptographyProvider. Since we do not\n * support local cryptography in the browser this replacement always returns false\n * for `supportsAlgorithm` and `supportsOperation` so that these methods should\n * never be called.\n */\nexport class RsaCryptographyProvider implements CryptographyProvider {\n  encrypt(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n  decrypt(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n\n  /**\n   * Browser RSA Provider does not support any algorithms or operations.\n   */\n  isSupported(): boolean {\n    return false;\n  }\n\n  wrapKey(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n\n  unwrapKey(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n\n  sign(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n\n  signData(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n\n  verify(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n\n  verifyData(): never {\n    throw new LocalCryptographyUnsupportedError(\n      \"RSA Local cryptography is not supported in the browser.\",\n    );\n  }\n}\n"]}