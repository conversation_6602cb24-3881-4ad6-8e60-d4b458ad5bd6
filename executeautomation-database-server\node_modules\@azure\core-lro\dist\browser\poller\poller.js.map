{"version": 3, "file": "poller.js", "sourceRoot": "", "sources": ["../../../src/poller/poller.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAYlC,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAChF,OAAO,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,MAAM,gBAAgB,GAGlB,GAAG,EAAE,CAAC,CAAC;IACT;;;;OAIG;IACH,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAAQ;IAC7D,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;IACnD,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;IACjD,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IACrD,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;IACjD,YAAY,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;IACrD,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;IAE/C,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK;IAChC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM;IAClC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,UAAU;IAClD,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,QAAQ;IAC9C,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS;IAChD,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW;CACrD,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAC/B,MAAmD;IAKnD,MAAM,EACJ,oBAAoB,EACpB,4BAA4B,EAC5B,yBAAyB,EACzB,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAClB,QAAQ,EACR,qBAAqB,GACtB,GAAG,MAAM,CAAC;IACX,OAAO,KAAK,EACV,EAAE,IAAI,EAAE,IAAI,EAA2D,EACvE,OAAyD,EACzD,EAAE;QACF,MAAM,EACJ,aAAa,EACb,WAAW,EACX,qBAAqB,EAAE,6BAA6B,EACpD,YAAY,GAAG,mBAAmB,EAClC,WAAW,GACZ,GAAG,OAAO,IAAI,EAAE,CAAC;QAClB,MAAM,UAAU,GAAG,gBAAgB,EAAmB,CAAC;QACvD,MAAM,qBAAqB,GAAG,6BAA6B;YACzD,CAAC,CAAC,CAAC,GAAG,EAAE;gBACJ,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,OAAO,CAAC,iBAAyB,EAAE,SAAkB,EAAE,EAAE;oBACvD,IAAI,SAAS;wBAAE,6BAA6B,CAAC,iBAAiB,CAAC,CAAC;yBAC3D,IAAI,CAAC,MAAM;wBAAE,6BAA6B,CAAC,iBAAiB,CAAC,CAAC;oBACnE,MAAM,GAAG,IAAI,CAAC;gBAChB,CAAC,CAAC;YACJ,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,SAAS,CAAC;QACd,MAAM,KAAK,GAAqC,WAAW;YACzD,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC;YAC/B,CAAC,CAAC,MAAM,aAAa,CAAC;gBAClB,IAAI;gBACJ,UAAU;gBACV,aAAa;gBACb,kBAAkB,EAAE,4BAA4B;gBAChD,qBAAqB;gBACrB,gBAAgB,EAAE,CAAC,qBAAqB;aACzC,CAAC,CAAC;QACP,IAAI,aAA2C,CAAC;QAChD,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAG9C,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAmB,CAAC;QAC5C,MAAM,oBAAoB,GAAG,KAAK,IAAmB,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1F,MAAM,YAAY,GAAG,wBAAwB,CAAC;QAC9C,IAAI,uBAAuB,GAAG,YAAY,CAAC;QAE3C,MAAM,MAAM,GAAsC;YAChD,iBAAiB,EAAE,GAAG,EAAE,CAAC,KAAK;YAC9B,SAAS,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM;YAC7B,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;YACxE,SAAS,EAAE,GAAG,EAAE,CAAC,aAAa,KAAK,SAAS;YAC5C,WAAW,EAAE,GAAG,EAAE;gBAChB,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC;YACD,QAAQ,EAAE,GAAG,EAAE,CACb,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK;aACN,CAAC;YACJ,UAAU,EAAE,CAAC,QAAiC,EAAE,EAAE;gBAChD,MAAM,CAAC,GAAG,MAAM,EAAE,CAAC;gBACnB,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC1B,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC;YACD,aAAa,EAAE,CAAC,WAA+C,EAAE,EAAE,CACjE,CAAC,aAAa,aAAb,aAAa,cAAb,aAAa,IAAb,aAAa,GAAK,CAAC,KAAK,IAAI,EAAE;gBAC7B,MAAM,EAAE,WAAW,EAAE,gBAAgB,EAAE,GAAG,WAAW,IAAI,EAAE,CAAC;gBAC5D,qDAAqD;gBACrD,SAAS,aAAa;oBACpB,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC1B,CAAC;gBACD,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,CAAC;gBAC3C,IAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,OAAO,EAAE,CAAC;oBAC9B,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC1B,CAAC;qBAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBAChC,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7E,CAAC;gBAED,IAAI,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;wBACrB,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;wBACnC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;4BACxB,MAAM,KAAK,CAAC,uBAAuB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;4BACtD,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;wBACrC,CAAC;oBACH,CAAC;gBACH,CAAC;wBAAS,CAAC;oBACT,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;gBAChE,CAAC;gBACD,IAAI,qBAAqB,EAAE,CAAC;oBAC1B,OAAO,MAAM,CAAC,SAAS,EAAa,CAAC;gBACvC,CAAC;qBAAM,CAAC;oBACN,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;wBACrB,KAAK,WAAW;4BACd,OAAO,MAAM,CAAC,SAAS,EAAa,CAAC;wBACvC,KAAK,UAAU;4BACb,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;wBAChC,KAAK,QAAQ;4BACX,MAAM,KAAK,CAAC,KAAK,CAAC;wBACpB,KAAK,YAAY,CAAC;wBAClB,KAAK,SAAS;4BACZ,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;oBACvE,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChB,aAAa,GAAG,SAAS,CAAC;YAC5B,CAAC,CAAC,EAAC;YACL,KAAK,CAAC,IAAI,CAAC,WAA+C;gBACxD,IAAI,qBAAqB,EAAE,CAAC;oBAC1B,IAAI,MAAM,CAAC,MAAM,EAAE;wBAAE,OAAO;gBAC9B,CAAC;qBAAM,CAAC;oBACN,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;wBACrB,KAAK,WAAW;4BACd,OAAO;wBACT,KAAK,UAAU;4BACb,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;wBAChC,KAAK,QAAQ;4BACX,MAAM,KAAK,CAAC,KAAK,CAAC;oBACtB,CAAC;gBACH,CAAC;gBACD,MAAM,aAAa,CAAC;oBAClB,IAAI;oBACJ,KAAK;oBACL,UAAU;oBACV,oBAAoB;oBACpB,gBAAgB;oBAChB,qBAAqB;oBACrB,kBAAkB;oBAClB,kBAAkB,EAAE,yBAAyB;oBAC7C,mBAAmB;oBACnB,aAAa;oBACb,QAAQ;oBACR,WAAW;oBACX,OAAO,EAAE,WAAW;oBACpB,QAAQ,EAAE,CAAC,gBAAgB,EAAE,EAAE;wBAC7B,uBAAuB,GAAG,gBAAgB,CAAC;oBAC7C,CAAC;oBACD,gBAAgB,EAAE,CAAC,qBAAqB;iBACzC,CAAC,CAAC;gBACH,MAAM,oBAAoB,EAAE,CAAC;gBAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC3B,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;wBACrB,KAAK,UAAU;4BACb,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;wBAChC,KAAK,QAAQ;4BACX,MAAM,KAAK,CAAC,KAAK,CAAC;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport {\n  BuildCreatePollerOptions,\n  CreatePollerOptions,\n  Operation,\n  OperationState,\n  RestorableOperationState,\n  SimplePollerLike,\n  StateProxy,\n} from \"./models.js\";\nimport { deserializeState, initOperation, pollOperation } from \"./operation.js\";\nimport { POLL_INTERVAL_IN_MS } from \"./constants.js\";\nimport { delay } from \"@azure/core-util\";\n\nconst createStateProxy: <TResult, TState extends OperationState<TResult>>() => StateProxy<\n  TState,\n  TResult\n> = () => ({\n  /**\n   * The state at this point is created to be of type OperationState<TResult>.\n   * It will be updated later to be of type TState when the\n   * customer-provided callback, `updateState`, is called during polling.\n   */\n  initState: (config) => ({ status: \"running\", config }) as any,\n  setCanceled: (state) => (state.status = \"canceled\"),\n  setError: (state, error) => (state.error = error),\n  setResult: (state, result) => (state.result = result),\n  setRunning: (state) => (state.status = \"running\"),\n  setSucceeded: (state) => (state.status = \"succeeded\"),\n  setFailed: (state) => (state.status = \"failed\"),\n\n  getError: (state) => state.error,\n  getResult: (state) => state.result,\n  isCanceled: (state) => state.status === \"canceled\",\n  isFailed: (state) => state.status === \"failed\",\n  isRunning: (state) => state.status === \"running\",\n  isSucceeded: (state) => state.status === \"succeeded\",\n});\n\n/**\n * Returns a poller factory.\n */\nexport function buildCreatePoller<TResponse, TResult, TState extends OperationState<TResult>>(\n  inputs: BuildCreatePollerOptions<TResponse, TState>,\n): (\n  lro: Operation<TResponse, { abortSignal?: AbortSignalLike }>,\n  options?: CreatePollerOptions<TResponse, TResult, TState>,\n) => Promise<SimplePollerLike<TState, TResult>> {\n  const {\n    getOperationLocation,\n    getStatusFromInitialResponse,\n    getStatusFromPollResponse,\n    isOperationError,\n    getResourceLocation,\n    getPollingInterval,\n    getError,\n    resolveOnUnsuccessful,\n  } = inputs;\n  return async (\n    { init, poll }: Operation<TResponse, { abortSignal?: AbortSignalLike }>,\n    options?: CreatePollerOptions<TResponse, TResult, TState>,\n  ) => {\n    const {\n      processResult,\n      updateState,\n      withOperationLocation: withOperationLocationCallback,\n      intervalInMs = POLL_INTERVAL_IN_MS,\n      restoreFrom,\n    } = options || {};\n    const stateProxy = createStateProxy<TResult, TState>();\n    const withOperationLocation = withOperationLocationCallback\n      ? (() => {\n          let called = false;\n          return (operationLocation: string, isUpdated: boolean) => {\n            if (isUpdated) withOperationLocationCallback(operationLocation);\n            else if (!called) withOperationLocationCallback(operationLocation);\n            called = true;\n          };\n        })()\n      : undefined;\n    const state: RestorableOperationState<TState> = restoreFrom\n      ? deserializeState(restoreFrom)\n      : await initOperation({\n          init,\n          stateProxy,\n          processResult,\n          getOperationStatus: getStatusFromInitialResponse,\n          withOperationLocation,\n          setErrorAsResult: !resolveOnUnsuccessful,\n        });\n    let resultPromise: Promise<TResult> | undefined;\n    const abortController = new AbortController();\n    // Progress handlers\n    type Handler = (state: TState) => void;\n    const handlers = new Map<symbol, Handler>();\n    const handleProgressEvents = async (): Promise<void> => handlers.forEach((h) => h(state));\n    const cancelErrMsg = \"Operation was canceled\";\n    let currentPollIntervalInMs = intervalInMs;\n\n    const poller: SimplePollerLike<TState, TResult> = {\n      getOperationState: () => state,\n      getResult: () => state.result,\n      isDone: () => [\"succeeded\", \"failed\", \"canceled\"].includes(state.status),\n      isStopped: () => resultPromise === undefined,\n      stopPolling: () => {\n        abortController.abort();\n      },\n      toString: () =>\n        JSON.stringify({\n          state,\n        }),\n      onProgress: (callback: (state: TState) => void) => {\n        const s = Symbol();\n        handlers.set(s, callback);\n        return () => handlers.delete(s);\n      },\n      pollUntilDone: (pollOptions?: { abortSignal?: AbortSignalLike }) =>\n        (resultPromise ??= (async () => {\n          const { abortSignal: inputAbortSignal } = pollOptions || {};\n          // In the future we can use AbortSignal.any() instead\n          function abortListener(): void {\n            abortController.abort();\n          }\n          const abortSignal = abortController.signal;\n          if (inputAbortSignal?.aborted) {\n            abortController.abort();\n          } else if (!abortSignal.aborted) {\n            inputAbortSignal?.addEventListener(\"abort\", abortListener, { once: true });\n          }\n\n          try {\n            if (!poller.isDone()) {\n              await poller.poll({ abortSignal });\n              while (!poller.isDone()) {\n                await delay(currentPollIntervalInMs, { abortSignal });\n                await poller.poll({ abortSignal });\n              }\n            }\n          } finally {\n            inputAbortSignal?.removeEventListener(\"abort\", abortListener);\n          }\n          if (resolveOnUnsuccessful) {\n            return poller.getResult() as TResult;\n          } else {\n            switch (state.status) {\n              case \"succeeded\":\n                return poller.getResult() as TResult;\n              case \"canceled\":\n                throw new Error(cancelErrMsg);\n              case \"failed\":\n                throw state.error;\n              case \"notStarted\":\n              case \"running\":\n                throw new Error(`Polling completed without succeeding or failing`);\n            }\n          }\n        })().finally(() => {\n          resultPromise = undefined;\n        })),\n      async poll(pollOptions?: { abortSignal?: AbortSignalLike }): Promise<void> {\n        if (resolveOnUnsuccessful) {\n          if (poller.isDone()) return;\n        } else {\n          switch (state.status) {\n            case \"succeeded\":\n              return;\n            case \"canceled\":\n              throw new Error(cancelErrMsg);\n            case \"failed\":\n              throw state.error;\n          }\n        }\n        await pollOperation({\n          poll,\n          state,\n          stateProxy,\n          getOperationLocation,\n          isOperationError,\n          withOperationLocation,\n          getPollingInterval,\n          getOperationStatus: getStatusFromPollResponse,\n          getResourceLocation,\n          processResult,\n          getError,\n          updateState,\n          options: pollOptions,\n          setDelay: (pollIntervalInMs) => {\n            currentPollIntervalInMs = pollIntervalInMs;\n          },\n          setErrorAsResult: !resolveOnUnsuccessful,\n        });\n        await handleProgressEvents();\n        if (!resolveOnUnsuccessful) {\n          switch (state.status) {\n            case \"canceled\":\n              throw new Error(cancelErrMsg);\n            case \"failed\":\n              throw state.error;\n          }\n        }\n      },\n    };\n    return poller;\n  };\n}\n"]}