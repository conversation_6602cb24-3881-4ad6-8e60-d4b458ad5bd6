# FileScopeMCP 安装配置指南

## 📋 项目概况

**项目名称**: FileScopeMCP  
**GitHub 仓库**: [admica/FileScopeMCP](https://github.com/admica/FileScopeMCP)  
**⭐ Stars**: 193 个  
**安装位置**: `F:\MCP\FileScopeMCP`  
**状态**: ✅ 已成功安装

## 🚀 安装过程记录

### 1. 环境准备

- ✅ Node.js 环境已就绪
- ✅ npm 包管理器可用
- ✅ Git 工具已安装

### 2. 项目克隆

```bash
# 在 F:\MCP 目录下执行
git clone https://github.com/admica/FileScopeMCP.git
cd FileScopeMCP
```

### 3. 依赖安装

```bash
# 安装项目依赖（已完成）
npm install
```

**安装结果**:
- ✅ 346 个包已安装
- ✅ 无安全漏洞
- ✅ node_modules 目录已创建

### 4. 项目构建状态

**注意**: 项目源代码存在格式问题，但预构建的 `dist` 目录可用。

- ✅ `dist/mcp-server.js` 存在
- ⚠️ 源代码格式化问题（storage-utils.ts 等文件）
- ⚠️ 构建过程中有导入错误

## 🔧 Augment Code 配置

### 方法一：使用 Augment 设置面板（推荐）

1. **打开设置面板**
   - 点击 Augment 面板右上角的齿轮图标

2. **添加 MCP 服务器**
   - 点击 **+ Add MCP** 按钮
   - 输入以下配置：

   **名称**: `FileScopeMCP`
   
   **命令**: `node`
   
   **参数**: 
   ```
   F:\MCP\FileScopeMCP\dist\mcp-server.js
   --base-dir=F:\MCP
   ```

3. **保存配置**
   - 点击 **Add** 按钮完成添加

### 方法二：手动编辑 settings.json

1. **访问设置文件**
   - 按 `Cmd/Ctrl + Shift + P`
   - 选择 "Edit Settings"
   - 点击 "Edit in settings.json"

2. **添加配置**
   ```json
   {
     "augment.advanced": {
       "mcpServers": [
         {
           "name": "FileScopeMCP",
           "command": "node",
           "args": [
             "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
             "--base-dir=F:\\MCP"
           ]
         }
       ]
     }
   }
   ```

3. **重启编辑器**
   - 保存文件后重启 Augment Code

## 🛠️ 可用工具功能

FileScopeMCP 提供以下 MCP 工具：

### 文件树管理
- `list_saved_trees` - 列出所有保存的文件树
- `create_file_tree` - 为特定目录创建新的文件树配置
- `select_file_tree` - 选择现有文件树进行工作
- `delete_file_tree` - 删除文件树配置

### 文件分析
- `list_files` - 列出项目中所有文件及其重要性排名
- `get_file_importance` - 获取特定文件的详细信息
- `find_important_files` - 查找项目中最重要的文件
- `read_file_content` - 读取特定文件的内容
- `recalculate_importance` - 重新计算所有文件的重要性值

### 文件摘要
- `get_file_summary` - 获取文件的存储摘要
- `set_file_summary` - 设置或更新文件摘要

### 文件监控
- `toggle_file_watching` - 开启/关闭文件监控
- `get_file_watching_status` - 获取文件监控状态
- `update_file_watching_config` - 更新文件监控配置

### 图表生成
- `generate_diagram` - 创建可自定义的 Mermaid 图表

## 🎯 使用示例

### 1. 分析当前项目

在 Augment Code 中，您可以这样使用：

```
请帮我分析 F:\MCP 项目的文件结构，找出最重要的文件
```

FileScopeMCP 将会：
1. 扫描项目目录
2. 分析文件依赖关系
3. 计算文件重要性评分
4. 返回重要文件列表

### 2. 生成依赖关系图

```
为 F:\MCP 项目生成一个依赖关系图表
```

FileScopeMCP 将会：
1. 分析文件间的依赖关系
2. 生成 Mermaid 格式的图表
3. 可选择输出为 HTML 或 PNG 格式

### 3. 文件摘要管理

```
为重要文件添加摘要说明
```

FileScopeMCP 可以：
1. 为关键文件添加描述性摘要
2. 管理文件的元数据信息
3. 提供文件的上下文说明

## ⚠️ 已知问题和解决方案

### 1. 源代码格式问题

**问题**: `src/storage-utils.ts` 等文件格式化异常
**影响**: 无法重新构建项目
**解决方案**: 使用预构建的 `dist` 目录文件

### 2. 导入错误

**问题**: 模块导入时出现 `SyntaxError`
**状态**: 需要进一步调试
**临时方案**: 使用现有的构建文件

### 3. 路径配置

**重要**: 确保在配置中使用正确的绝对路径：
- 使用 `F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js`
- 基础目录设置为 `F:\\MCP`

## 🔍 故障排除

### 1. MCP 服务器无法启动

**检查项目**:
```bash
cd F:\MCP\FileScopeMCP
node dist/mcp-server.js --help
```

**常见解决方案**:
- 确认 Node.js 版本 >= 18
- 检查文件路径是否正确
- 验证依赖是否完整安装

### 2. Augment Code 无法识别

**检查配置**:
1. 验证 settings.json 语法正确
2. 确认路径使用反斜杠转义 `\\`
3. 重启 Augment Code

### 3. 权限问题

**Windows 特定**:
- 确保对 `F:\MCP` 目录有读写权限
- 以管理员身份运行 Augment Code（如需要）

## 📚 参考资源

- **项目文档**: `F:\MCP\doc\FileScopeMCP_官方使用指南.md`
- **GitHub 仓库**: [https://github.com/admica/FileScopeMCP](https://github.com/admica/FileScopeMCP)
- **Augment MCP 指南**: `F:\MCP\doc\Augment_MCP_安装配置指南.md`

## 🎉 安装完成

FileScopeMCP 已成功安装到本项目中：

- ✅ **安装位置**: `F:\MCP\FileScopeMCP`
- ✅ **依赖管理**: 使用 npm，避免全局污染
- ✅ **构建文件**: `dist/mcp-server.js` (56KB) 已就绪
- ✅ **配置文件**: `augment_config.json` 已生成
- ✅ **工具丰富**: 提供完整的代码分析功能

## 🚀 快速配置步骤

### 1. 复制配置到 Augment Code

打开 `F:\MCP\FileScopeMCP\augment_config.json` 文件，复制以下配置：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": [
          "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
          "--base-dir=F:\\MCP"
        ]
      }
    ]
  }
}
```

### 2. 在 Augment Code 中配置

**方法A: 使用设置面板**
1. 点击 Augment 面板右上角的齿轮图标
2. 点击 **+ Add MCP** 按钮
3. 填入：
   - 名称: `FileScopeMCP`
   - 命令: `node`
   - 参数: `F:\MCP\FileScopeMCP\dist\mcp-server.js --base-dir=F:\MCP`

**方法B: 编辑 settings.json**
1. 按 `Ctrl+Shift+P` → "Edit Settings" → "Edit in settings.json"
2. 将上述 JSON 配置添加到文件中
3. 保存并重启 Augment Code

### 3. 验证安装

重启 Augment Code 后，尝试以下命令：

```
请帮我分析 F:\MCP 项目的文件结构，找出最重要的文件
```

如果 FileScopeMCP 正常工作，它将：
- 扫描项目目录
- 分析文件依赖关系
- 返回重要文件列表和依赖图表

## 📋 安装验证清单

- [x] **项目克隆**: GitHub 仓库已克隆到本地
- [x] **依赖安装**: 346 个 npm 包已安装
- [x] **构建文件**: dist/mcp-server.js 存在且完整
- [x] **配置生成**: augment_config.json 已创建
- [x] **文档完整**: 安装和使用指南已准备
- [ ] **Augment 配置**: 需要手动添加到 Augment Code
- [ ] **功能测试**: 需要在 Augment 中验证工具可用性

现在您可以在 Augment Code 中使用 FileScopeMCP 来分析项目结构、追踪依赖关系和生成可视化图表了！

---

💡 **提示**: FileScopeMCP 特别适合大型项目的代码理解和重构工作，建议结合 AI 助手使用以获得最佳效果！
