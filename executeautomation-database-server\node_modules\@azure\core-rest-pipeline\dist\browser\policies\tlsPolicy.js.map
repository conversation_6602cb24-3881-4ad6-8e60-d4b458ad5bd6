{"version": 3, "file": "tlsPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/tlsPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EACL,SAAS,IAAI,YAAY,EACzB,aAAa,IAAI,gBAAgB,GAClC,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,gBAAgB,CAAC;AAE9C;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,WAAyB;IACjD,OAAO,YAAY,CAAC,WAAW,CAAC,CAAC;AACnC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { TlsSettings } from \"../interfaces.js\";\n\nimport {\n  tlsPolicy as tspTlsPolicy,\n  tlsPolicyName as tspTlsPolicyName,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * Name of the TLS Policy\n */\nexport const tlsPolicyName = tspTlsPolicyName;\n\n/**\n * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.\n */\nexport function tlsPolicy(tlsSettings?: TlsSettings): PipelinePolicy {\n  return tspTlsPolicy(tlsSettings);\n}\n"]}