# Augment MCP 手动添加完整指南

## 📋 概述

本文档详细介绍如何在 Augment Code 中手动添加 MCP 服务器，包括两种主要方法：Import from JSON 和手动编辑 settings.json。基于实际安装和配置经验总结。

## 🚀 方法一：Import from JSON（推荐）

### 1.1 JSON 配置格式

**正确的 Import JSON 格式**（单个 MCP 服务器配置）：

```json
{
  "name": "服务器名称",
  "command": "启动命令",
  "args": [
    "参数1",
    "参数2"
  ]
}
```

### 1.2 具体示例

#### FileScopeMCP 配置示例
```json
{
  "name": "FileScopeMCP",
  "command": "node",
  "args": [
    "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
    "--base-dir=F:\\MCP"
  ]
}
```

#### GitHub MCP Server 配置示例
```json
{
  "name": "GitH<PERSON>",
  "command": "F:\\MCP\\github-mcp-server\\github-mcp-server.exe",
  "args": []
}
```

#### Tencent TAT MCP Server 配置示例
```json
{
  "name": "TencentTAT",
  "command": "F:\\MCP\\tat-mcp-server\\env\\Scripts\\mcp-server-tat.exe",
  "args": []
}
```

### 1.3 Import 操作步骤

1. **准备配置文件**
   - 创建 `.json` 文件，使用上述格式
   - 确保 JSON 语法正确（无多余逗号、括号匹配）

2. **在 Augment Code 中导入**
   - 打开 Augment Code
   - 点击右上角齿轮图标 ⚙️ 进入设置
   - 找到 "MCP Servers" 或 "Tools" 部分
   - 点击 "Import MCP Server" 或 "Import from JSON"
   - 选择准备好的 JSON 配置文件
   - 确认导入

3. **验证导入结果**
   - 检查 MCP 服务器是否出现在列表中
   - 查看状态是否为 "Active" 或 "Connected"

## 🔧 方法二：手动编辑 settings.json

### 2.1 访问 settings.json

1. **打开设置编辑器**
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 选择 "Edit Settings"
   - 在 Advanced 下，点击 "Edit in settings.json"

### 2.2 完整配置格式

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": [
          "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
          "--base-dir=F:\\MCP"
        ]
      },
      {
        "name": "GitHub",
        "command": "F:\\MCP\\github-mcp-server\\github-mcp-server.exe",
        "args": []
      },
      {
        "name": "TencentTAT",
        "command": "F:\\MCP\\tat-mcp-server\\env\\Scripts\\mcp-server-tat.exe",
        "args": []
      }
    ]
  }
}
```

### 2.3 添加新服务器

如果已有其他配置，只需在 `mcpServers` 数组中添加新的服务器配置：

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "现有服务器",
        "command": "现有命令",
        "args": ["现有参数"]
      },
      {
        "name": "新服务器",
        "command": "新命令",
        "args": ["新参数"]
      }
    ]
  }
}
```

## ⚠️ 重要注意事项

### 3.1 路径格式要求

**Windows 路径处理**：
- 使用双反斜杠转义：`"F:\\MCP\\server.exe"`
- 或使用正斜杠：`"F:/MCP/server.exe"`
- 避免单反斜杠，会导致 JSON 解析错误

**示例对比**：
```json
// ✅ 正确格式
"F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js"
"F:/MCP/FileScopeMCP/dist/mcp-server.js"

// ❌ 错误格式
"F:\MCP\FileScopeMCP\dist\mcp-server.js"
```

### 3.2 JSON 语法检查

**常见错误**：
- 缺少逗号或多余逗号
- 括号不匹配
- 引号不匹配
- 数组格式错误

**正确示例**：
```json
{
  "name": "ServerName",
  "command": "command",
  "args": [
    "arg1",
    "arg2"
  ]
}
```

## 📊 成功经验总结

### 4.1 FileScopeMCP 成功配置

**配置要点**：
- ✅ 使用 `node` 作为启动命令
- ✅ 指向 `dist/mcp-server.js` 构建文件
- ✅ 添加 `--base-dir` 参数指定工作目录
- ✅ 修复了源代码格式问题后重新构建

**成功配置**：
```json
{
  "name": "FileScopeMCP",
  "command": "node",
  "args": [
    "F:\\MCP\\FileScopeMCP\\dist\\mcp-server.js",
    "--base-dir=F:\\MCP"
  ]
}
```

**验证方法**：
```
请帮我分析 F:\MCP 项目的文件结构，找出最重要的文件
```

### 4.2 GitHub MCP Server 成功配置

**配置要点**：
- ✅ 直接使用编译好的 `.exe` 文件
- ✅ 无需额外参数
- ✅ 确保可执行文件存在且有执行权限

**成功配置**：
```json
{
  "name": "GitHub",
  "command": "F:\\MCP\\github-mcp-server\\github-mcp-server.exe",
  "args": []
}
```

### 4.3 Tencent TAT MCP Server 成功配置

**配置要点**：
- ✅ 使用虚拟环境中的可执行文件
- ✅ 路径指向 `env\Scripts\mcp-server-tat.exe`
- ✅ 确保 Python 虚拟环境正确配置

**成功配置**：
```json
{
  "name": "TencentTAT",
  "command": "F:\\MCP\\tat-mcp-server\\env\\Scripts\\mcp-server-tat.exe",
  "args": []
}
```

## ❌ 失败经验总结

### 5.1 JSON 格式错误

**问题**: Import from JSON 功能报错 "Failed to parse MCP servers from JSON"

**原因**: 使用了完整的 settings.json 格式而不是单个服务器配置格式

**错误示例**：
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "FileScopeMCP",
        "command": "node",
        "args": ["..."]
      }
    ]
  }
}
```

**解决方案**: 使用简化的单服务器格式：
```json
{
  "name": "FileScopeMCP",
  "command": "node",
  "args": ["..."]
}
```

### 5.2 源代码格式问题

**问题**: MCP 服务器启动失败，模块导入错误

**原因**: TypeScript 源文件被压缩成一行，导致构建失败

**错误信息**：
```
SyntaxError: The requested module './storage-utils.js' does not provide an export named 'createFileTreeConfig'
```

**解决方案**: 
1. 手动重新格式化源代码文件
2. 重新运行 `npm run build`
3. 验证构建产物正确

### 5.3 路径配置错误

**问题**: 服务器无法启动，找不到文件

**常见错误**：
- 使用相对路径而非绝对路径
- Windows 路径反斜杠未正确转义
- 指向错误的文件（如源文件而非构建文件）

**解决方案**：
- 始终使用绝对路径
- 正确转义 Windows 路径
- 确认文件确实存在

### 5.4 权限问题

**问题**: 可执行文件无法启动

**解决方案**：
- 确保文件有执行权限
- 以管理员身份运行 Augment Code（如需要）
- 检查防病毒软件是否阻止执行

## 🔍 故障排除步骤

### 6.1 基本检查清单

- [ ] JSON 语法是否正确
- [ ] 文件路径是否存在
- [ ] 路径格式是否正确（反斜杠转义）
- [ ] 可执行文件是否有权限
- [ ] 依赖是否正确安装

### 6.2 测试方法

1. **命令行测试**：
   ```bash
   # 测试 Node.js 服务器
   node F:\MCP\FileScopeMCP\dist\mcp-server.js --help
   
   # 测试可执行文件
   F:\MCP\github-mcp-server\github-mcp-server.exe --help
   ```

2. **Augment 中测试**：
   - 重启 Augment Code
   - 查看 MCP 服务器状态
   - 尝试使用服务器功能

### 6.3 日志查看

- 查看 Augment Code 的错误日志
- 检查控制台输出
- 查看服务器启动信息

## 📚 配置模板

### 7.1 Import JSON 模板

创建文件：`mcp-server-config.json`

```json
{
  "name": "YOUR_SERVER_NAME",
  "command": "YOUR_COMMAND",
  "args": [
    "ARG1",
    "ARG2"
  ]
}
```

### 7.2 settings.json 模板

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "YOUR_SERVER_NAME",
        "command": "YOUR_COMMAND",
        "args": [
          "ARG1",
          "ARG2"
        ]
      }
    ]
  }
}
```

## 🎯 最佳实践

1. **优先使用 Import from JSON** - 更简单，错误更少
2. **使用绝对路径** - 避免路径解析问题
3. **先命令行测试** - 确保服务器能正常启动
4. **保存配置备份** - 便于恢复和分享
5. **逐个添加服务器** - 便于排查问题
6. **验证每个配置** - 确保功能正常工作

---

💡 **提示**: 遇到问题时，先检查 JSON 格式和文件路径，这是最常见的错误来源！
