{"version": 3, "file": "remoteCryptographyProvider.js", "sourceRoot": "", "sources": ["../../../src/cryptography/remoteCryptographyProvider.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAoBlC,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAG9C,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAE,0BAA0B,EAAE,MAAM,kBAAkB,CAAC;AAE9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AACtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEzC,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AACnC,OAAO,EAAE,4BAA4B,EAAE,MAAM,wBAAwB,CAAC;AACtE,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,EAAE,mCAAmC,EAAE,MAAM,2BAA2B,CAAC;AAEhF;;;GAGG;AACH,MAAM,OAAO,0BAA0B;IACrC,YACE,GAAyB,EACzB,UAA2B,EAC3B,kBAA6C,EAAE;;QAE/C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,IAAI,KAAa,CAAC;QAClB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,KAAK,GAAG,GAAG,CAAC;QACd,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,GAAG,CAAC,EAAG,CAAC;QAClB,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,0BAA0B,CAAC,KAAK,CAAC,CAAC;YACjD,IAAI,MAAM,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;gBAC/C,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAChC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,MAAA,MAAM,CAAC,OAAO,mCAAI,EAAE,CAAC;YAEpC,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAElB,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,kCAAkC,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,gEAAgE;IAChE,WAAW,CAAC,UAAkB,EAAE,UAAyC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,iBAAoC,EACpC,UAA0B,EAAE;QAE5B,MAAM,EAAE,SAAS,EAAE,SAAS,KAAgB,iBAAiB,EAA5B,MAAM,UAAK,iBAAiB,EAAvD,0BAAmC,CAAoB,CAAC;QAC9D,MAAM,cAAc,mCAAQ,OAAO,GAAK,MAAM,CAAE,CAAC;QAEjD,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,cAAc,EACd,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CACtC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,KAAK,EAAE,SAAS;gBAChB,GAAG,EACD,6BAA6B,IAAI,iBAAiB;oBAChD,CAAC,CAAC,iBAAiB,CAAC,2BAA2B;oBAC/C,CAAC,CAAC,SAAS;gBACf,EAAE,EAAE,IAAI,IAAI,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;aACjE,EACD,cAAc,CACf,CAAC;YAEF,OAAO;gBACL,SAAS,EAAE,iBAAiB,CAAC,SAAS;gBACtC,MAAM,EAAE,MAAM,CAAC,MAAO;gBACtB,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACtB,2BAA2B,EAAE,MAAM,CAAC,2BAA2B;gBAC/D,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;gBAC3C,EAAE,EAAE,MAAM,CAAC,EAAE;aACd,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iBAAoC,EACpC,UAA0B,EAAE;QAE5B,MAAM,EAAE,SAAS,EAAE,UAAU,KAAgB,iBAAiB,EAA5B,MAAM,UAAK,iBAAiB,EAAxD,2BAAoC,CAAoB,CAAC;QAC/D,MAAM,cAAc,mCAAQ,OAAO,GAAK,MAAM,CAAE,CAAC;QAEjD,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,cAAc,EACd,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CACtC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,KAAK,EAAE,UAAU;gBACjB,GAAG,EACD,6BAA6B,IAAI,iBAAiB;oBAChD,CAAC,CAAC,iBAAiB,CAAC,2BAA2B;oBAC/C,CAAC,CAAC,SAAS;gBACf,EAAE,EAAE,IAAI,IAAI,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;gBAChE,GAAG,EACD,mBAAmB,IAAI,iBAAiB;oBACtC,CAAC,CAAC,iBAAiB,CAAC,iBAAiB;oBACrC,CAAC,CAAC,SAAS;aAChB,EACD,cAAc,CACf,CAAC;YACF,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAO;gBACtB,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACtB,SAAS;aACV,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED,OAAO,CACL,SAA2B,EAC3B,SAAqB,EACrB,UAA0B,EAAE;QAE5B,OAAO,aAAa,CAAC,QAAQ,CAC3B,oCAAoC,EACpC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CACtC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,KAAK,EAAE,SAAS;aACjB,EACD,cAAc,CACf,CAAC;YAEF,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAO;gBACtB,SAAS;gBACT,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;aACvB,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED,SAAS,CACP,SAA2B,EAC3B,YAAwB,EACxB,UAA4B,EAAE;QAE9B,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CACxC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,KAAK,EAAE,YAAY;aACpB,EACD,cAAc,CACf,CAAC;YAEF,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAO;gBACtB,SAAS;gBACT,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;aACvB,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,SAAiB,EAAE,MAAkB,EAAE,UAAuB,EAAE;QACnE,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACnC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,KAAK,EAAE,MAAM;aACd,EACD,cAAc,CACf,CAAC;YAEF,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,MAAO,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;QACvE,CAAC,CACF,CAAC;IACJ,CAAC;IAED,UAAU,CACR,SAAiB,EACjB,IAAgB,EAChB,SAAqB,EACrB,UAAyB,EAAE;QAE3B,OAAO,aAAa,CAAC,QAAQ,CAC3B,uCAAuC,EACvC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;QACjE,CAAC,CACF,CAAC;IACJ,CAAC;IAED,MAAM,CACJ,SAAiB,EACjB,MAAkB,EAClB,SAAqB,EACrB,UAAyB,EAAE;QAE3B,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACvC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,MAAM;gBACN,SAAS;aACV,EACD,cAAc,CACf,CAAC;YACF,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;gBAC/C,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;aACvB,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED,QAAQ,CAAC,SAAiB,EAAE,IAAgB,EAAE,UAAuB,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACnC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ;gBACE,SAAS;gBACT,KAAK,EAAE,MAAM;aACd,EACD,cAAc,CACf,CAAC;YACF,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,MAAO,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;QACvE,CAAC,CACF,CAAC;IACJ,CAAC;IAOD;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,UAAyB,EAAE;QAChC,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACjC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;gBACvD,CAAC;gBACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACvC,IAAI,CAAC,IAAI,EACT,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC/E,cAAc,CACf,CAAC;gBACF,IAAI,CAAC,GAAG,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB,CAAC,CACF,CAAC;IACJ,CAAC;IAwBD;;OAEG;IACK,QAAQ;QACd,IAAI,GAAG,CAAC;QACR,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YACjC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACjB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAED;;;;;;;;GAQG;AACH,SAAS,qBAAqB,CAC5B,QAAgB,EAChB,UAA2B,EAC3B,OAAyE;IAEzE,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC5B,OAAO,OAAO,CAAC,eAAe,CAAC;IACjC,CAAC;IAED,MAAM,OAAO,GAAG,0BAA0B,WAAW,EAAE,CAAC;IAExD,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAElD,OAAO,CAAC,gBAAgB,GAAG;QACzB,eAAe,EACb,gBAAgB,IAAI,gBAAgB,CAAC,eAAe;YAClD,CAAC,CAAC,GAAG,gBAAgB,CAAC,eAAe,IAAI,OAAO,EAAE;YAClD,CAAC,CAAC,OAAO;KACd,CAAC;IAEF,MAAM,uBAAuB,mCACxB,OAAO,KACV,UAAU,EAAE,OAAO,CAAC,cAAc,IAAI,kBAAkB,EACxD,cAAc,EAAE;YACd,MAAM,EAAE,MAAM,CAAC,IAAI;YACnB,4BAA4B,EAAE;gBAC5B,sBAAsB;gBACtB,4BAA4B;gBAC5B,+BAA+B;aAChC;SACF,GACF,CAAC;IAEF,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE,uBAAuB,CAAC,CAAC;IAEjF,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAAC,CAAC;IAC5E,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,4BAA4B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7E,yEAAyE;IACzE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QACxB,IAAI,EAAE,mBAAmB;QACzB,WAAW,CAAC,OAAO,EAAE,IAAI;;YACvB,MAAM,WAAW,GAAG,MAAA,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,mCAAI,EAAE,CAAC;YAC9D,IAAI,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC/C,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAC1D,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCredential } from \"@azure/core-auth\";\n\nimport type {\n  DecryptOptions,\n  DecryptParameters,\n  DecryptResult,\n  EncryptOptions,\n  EncryptParameters,\n  EncryptResult,\n  KeyWrapAlgorithm,\n  SignOptions,\n  SignResult,\n  UnwrapKeyOptions,\n  VerifyOptions,\n  VerifyResult,\n  WrapKeyOptions,\n  WrapResult,\n} from \"../cryptographyClientModels.js\";\nimport { SDK_VERSION } from \"../constants.js\";\nimport type { UnwrapResult } from \"../cryptographyClientModels.js\";\nimport type { KeyVaultClientOptionalParams } from \"../generated/index.js\";\nimport { KeyVaultClient } from \"../generated/index.js\";\nimport { parseKeyVaultKeyIdentifier } from \"../identifier.js\";\nimport type { CryptographyClientOptions, GetKeyOptions, KeyVaultKey } from \"../keysModels.js\";\nimport { LATEST_API_VERSION } from \"../keysModels.js\";\nimport { getKeyFromKeyBundle } from \"../transformations.js\";\nimport { createHash } from \"./crypto.js\";\nimport type { CryptographyProvider, CryptographyProviderOperation } from \"./models.js\";\nimport { logger } from \"../log.js\";\nimport { keyVaultAuthenticationPolicy } from \"@azure/keyvault-common\";\nimport { tracingClient } from \"../tracing.js\";\nimport { bearerTokenAuthenticationPolicyName } from \"@azure/core-rest-pipeline\";\n\n/**\n * The remote cryptography provider is used to run crypto operations against KeyVault.\n * @internal\n */\nexport class RemoteCryptographyProvider implements CryptographyProvider {\n  constructor(\n    key: string | KeyVaultKey,\n    credential: TokenCredential,\n    pipelineOptions: CryptographyClientOptions = {},\n  ) {\n    this.key = key;\n\n    let keyId: string;\n    if (typeof key === \"string\") {\n      keyId = key;\n    } else {\n      keyId = key.id!;\n    }\n\n    try {\n      const parsed = parseKeyVaultKeyIdentifier(keyId);\n      if (parsed.name === \"\") {\n        throw new Error(\"Could not find 'name' of key in key URL\");\n      }\n\n      if (!parsed.vaultUrl || parsed.vaultUrl === \"\") {\n        throw new Error(\"Could not find 'vaultUrl' of key in key URL\");\n      }\n\n      this.vaultUrl = parsed.vaultUrl;\n      this.name = parsed.name;\n      this.version = parsed.version ?? \"\";\n\n      this.client = getOrInitializeClient(this.vaultUrl, credential, pipelineOptions);\n    } catch (err: any) {\n      logger.error(err);\n\n      throw new Error(`${keyId} is not a valid Key Vault key ID`);\n    }\n  }\n\n  // The remote client supports all algorithms and all operations.\n  isSupported(_algorithm: string, _operation: CryptographyProviderOperation): boolean {\n    return true;\n  }\n\n  encrypt(\n    encryptParameters: EncryptParameters,\n    options: EncryptOptions = {},\n  ): Promise<EncryptResult> {\n    const { algorithm, plaintext, ...params } = encryptParameters;\n    const requestOptions = { ...options, ...params };\n\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.encrypt\",\n      requestOptions,\n      async (updatedOptions) => {\n        const result = await this.client.encrypt(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            value: plaintext,\n            aad:\n              \"additionalAuthenticatedData\" in encryptParameters\n                ? encryptParameters.additionalAuthenticatedData\n                : undefined,\n            iv: \"iv\" in encryptParameters ? encryptParameters.iv : undefined,\n          },\n          updatedOptions,\n        );\n\n        return {\n          algorithm: encryptParameters.algorithm,\n          result: result.result!,\n          keyID: this.getKeyID(),\n          additionalAuthenticatedData: result.additionalAuthenticatedData,\n          authenticationTag: result.authenticationTag,\n          iv: result.iv,\n        };\n      },\n    );\n  }\n\n  decrypt(\n    decryptParameters: DecryptParameters,\n    options: DecryptOptions = {},\n  ): Promise<DecryptResult> {\n    const { algorithm, ciphertext, ...params } = decryptParameters;\n    const requestOptions = { ...options, ...params };\n\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.decrypt\",\n      requestOptions,\n      async (updatedOptions) => {\n        const result = await this.client.decrypt(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            value: ciphertext,\n            aad:\n              \"additionalAuthenticatedData\" in decryptParameters\n                ? decryptParameters.additionalAuthenticatedData\n                : undefined,\n            iv: \"iv\" in decryptParameters ? decryptParameters.iv : undefined,\n            tag:\n              \"authenticationTag\" in decryptParameters\n                ? decryptParameters.authenticationTag\n                : undefined,\n          },\n          updatedOptions,\n        );\n        return {\n          result: result.result!,\n          keyID: this.getKeyID(),\n          algorithm,\n        };\n      },\n    );\n  }\n\n  wrapKey(\n    algorithm: KeyWrapAlgorithm,\n    keyToWrap: Uint8Array,\n    options: WrapKeyOptions = {},\n  ): Promise<WrapResult> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.wrapKey\",\n      options,\n      async (updatedOptions) => {\n        const result = await this.client.wrapKey(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            value: keyToWrap,\n          },\n          updatedOptions,\n        );\n\n        return {\n          result: result.result!,\n          algorithm,\n          keyID: this.getKeyID(),\n        };\n      },\n    );\n  }\n\n  unwrapKey(\n    algorithm: KeyWrapAlgorithm,\n    encryptedKey: Uint8Array,\n    options: UnwrapKeyOptions = {},\n  ): Promise<UnwrapResult> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.unwrapKey\",\n      options,\n      async (updatedOptions) => {\n        const result = await this.client.unwrapKey(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            value: encryptedKey,\n          },\n          updatedOptions,\n        );\n\n        return {\n          result: result.result!,\n          algorithm,\n          keyID: this.getKeyID(),\n        };\n      },\n    );\n  }\n\n  sign(algorithm: string, digest: Uint8Array, options: SignOptions = {}): Promise<SignResult> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.sign\",\n      options,\n      async (updatedOptions) => {\n        const result = await this.client.sign(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            value: digest,\n          },\n          updatedOptions,\n        );\n\n        return { result: result.result!, algorithm, keyID: this.getKeyID() };\n      },\n    );\n  }\n\n  verifyData(\n    algorithm: string,\n    data: Uint8Array,\n    signature: Uint8Array,\n    options: VerifyOptions = {},\n  ): Promise<VerifyResult> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.verifyData\",\n      options,\n      async (updatedOptions) => {\n        const hash = await createHash(algorithm, data);\n        return this.verify(algorithm, hash, signature, updatedOptions);\n      },\n    );\n  }\n\n  verify(\n    algorithm: string,\n    digest: Uint8Array,\n    signature: Uint8Array,\n    options: VerifyOptions = {},\n  ): Promise<VerifyResult> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.verify\",\n      options,\n      async (updatedOptions) => {\n        const response = await this.client.verify(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            digest,\n            signature,\n          },\n          updatedOptions,\n        );\n        return {\n          result: response.value ? response.value : false,\n          keyID: this.getKeyID(),\n        };\n      },\n    );\n  }\n\n  signData(algorithm: string, data: Uint8Array, options: SignOptions = {}): Promise<SignResult> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.signData\",\n      options,\n      async (updatedOptions) => {\n        const digest = await createHash(algorithm, data);\n        const result = await this.client.sign(\n          this.name,\n          this.version,\n          {\n            algorithm,\n            value: digest,\n          },\n          updatedOptions,\n        );\n        return { result: result.result!, algorithm, keyID: this.getKeyID() };\n      },\n    );\n  }\n\n  /**\n   * The base URL to the vault.\n   */\n  readonly vaultUrl: string;\n\n  /**\n   * The ID of the key used to perform cryptographic operations for the client.\n   */\n  get keyId(): string | undefined {\n    return this.getKeyID();\n  }\n\n  /**\n   * Gets the {@link KeyVaultKey} used for cryptography operations, fetching it\n   * from KeyVault if necessary.\n   * @param options - Additional options.\n   */\n  getKey(options: GetKeyOptions = {}): Promise<KeyVaultKey> {\n    return tracingClient.withSpan(\n      \"RemoteCryptographyProvider.getKey\",\n      options,\n      async (updatedOptions) => {\n        if (typeof this.key === \"string\") {\n          if (!this.name || this.name === \"\") {\n            throw new Error(\"getKey requires a key with a name\");\n          }\n          const response = await this.client.getKey(\n            this.name,\n            options && options.version ? options.version : this.version ? this.version : \"\",\n            updatedOptions,\n          );\n          this.key = getKeyFromKeyBundle(response);\n        }\n        return this.key;\n      },\n    );\n  }\n\n  /**\n   * A reference to the auto-generated KeyVault HTTP client.\n   */\n  private client: KeyVaultClient;\n\n  /**\n   * A reference to the key used for the cryptographic operations.\n   * Based on what was provided to the CryptographyClient constructor,\n   * it can be either a string with the URL of a Key Vault Key, or an already parsed {@link KeyVaultKey}.\n   */\n  private key: string | KeyVaultKey;\n\n  /**\n   * Name of the key the client represents\n   */\n  private name: string;\n\n  /**\n   * Version of the key the client represents\n   */\n  private version: string;\n\n  /**\n   * Attempts to retrieve the ID of the key.\n   */\n  private getKeyID(): string | undefined {\n    let kid;\n    if (typeof this.key !== \"string\") {\n      kid = this.key.id;\n    } else {\n      kid = this.key;\n    }\n\n    return kid;\n  }\n}\n\n/**\n * A helper method to either get the passed down generated client or initialize a new one.\n * An already constructed generated client may be passed down from {@link KeyClient} in which case we should reuse it.\n *\n * @internal\n * @param credential - The credential to use when initializing a new client.\n * @param options - The options for constructing a client or the underlying client if one already exists.\n * @returns - A generated client instance\n */\nfunction getOrInitializeClient(\n  vaultUrl: string,\n  credential: TokenCredential,\n  options: CryptographyClientOptions & { generatedClient?: KeyVaultClient },\n): KeyVaultClient {\n  if (options.generatedClient) {\n    return options.generatedClient;\n  }\n\n  const libInfo = `azsdk-js-keyvault-keys/${SDK_VERSION}`;\n\n  const userAgentOptions = options.userAgentOptions;\n\n  options.userAgentOptions = {\n    userAgentPrefix:\n      userAgentOptions && userAgentOptions.userAgentPrefix\n        ? `${userAgentOptions.userAgentPrefix} ${libInfo}`\n        : libInfo,\n  };\n\n  const internalPipelineOptions: KeyVaultClientOptionalParams = {\n    ...options,\n    apiVersion: options.serviceVersion || LATEST_API_VERSION,\n    loggingOptions: {\n      logger: logger.info,\n      additionalAllowedHeaderNames: [\n        \"x-ms-keyvault-region\",\n        \"x-ms-keyvault-network-info\",\n        \"x-ms-keyvault-service-version\",\n      ],\n    },\n  };\n\n  const client = new KeyVaultClient(vaultUrl, credential, internalPipelineOptions);\n\n  client.pipeline.removePolicy({ name: bearerTokenAuthenticationPolicyName });\n  client.pipeline.addPolicy(keyVaultAuthenticationPolicy(credential, options));\n  // Workaround for: https://github.com/Azure/azure-sdk-for-js/issues/31843\n  client.pipeline.addPolicy({\n    name: \"ContentTypePolicy\",\n    sendRequest(request, next) {\n      const contentType = request.headers.get(\"Content-Type\") ?? \"\";\n      if (contentType.startsWith(\"application/json\")) {\n        request.headers.set(\"Content-Type\", \"application/json\");\n      }\n      return next(request);\n    },\n  });\n\n  return client;\n}\n"]}