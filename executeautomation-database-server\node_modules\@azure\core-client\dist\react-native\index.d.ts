export { createSerializer, MapperTypeNames } from "./serializer.js";
export { ServiceClient, ServiceClientOptions } from "./serviceClient.js";
export { createClientPipeline, InternalClientPipelineOptions } from "./pipeline.js";
export { OperationSpec, OperationArguments, OperationOptions, OperationResponseMap, OperationParameter, OperationQueryParameter, OperationURLParameter, Serializer, BaseMapper, Mapper, MapperType, SimpleMapperType, EnumMapper, EnumMapperType, SequenceMapper, SequenceMapperType, DictionaryMapper, DictionaryMapperType, CompositeMapper, CompositeMapperType, MapperConstraints, OperationRequest, OperationRequestOptions, OperationRequestInfo, QueryCollectionFormat, ParameterPath, FullOperationResponse, PolymorphicDiscriminator, SpanConfig, XML_ATTRKEY, XML_CHARKEY, XmlOptions, SerializerOptions, RawResponseCallback, CommonClientOptions, AdditionalPolicyConfig, } from "./interfaces.js";
export { deserializationPolicy, deserializationPolicyName, DeserializationPolicyOptions, DeserializationContentTypes, } from "./deserializationPolicy.js";
export { serializationPolicy, serializationPolicyName, SerializationPolicyOptions, } from "./serializationPolicy.js";
export { authorizeRequestOnClaimChallenge } from "./authorizeRequestOnClaimChallenge.js";
export { authorizeRequestOnTenantChallenge } from "./authorizeRequestOnTenantChallenge.js";
//# sourceMappingURL=index.d.ts.map