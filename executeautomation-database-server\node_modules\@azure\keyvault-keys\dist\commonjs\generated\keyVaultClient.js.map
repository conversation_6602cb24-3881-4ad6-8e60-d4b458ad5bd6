{"version": 3, "file": "keyVaultClient.js", "sourceRoot": "", "sources": ["../../../src/generated/keyVaultClient.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,6CAIwB;AAiDxB,uDA0B6B;AAO7B,MAAa,cAAc;IAKzB,qHAAqH;IACrH,YACE,aAAqB,EACrB,UAA2B,EAC3B,UAAwC,EAAE;;QAE1C,MAAM,iBAAiB,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,0CAAE,eAAe,CAAC;QACrE,MAAM,eAAe,GAAG,iBAAiB;YACvC,CAAC,CAAC,GAAG,iBAAiB,kBAAkB;YACxC,CAAC,CAAC,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAA,yBAAc,EAAC,aAAa,EAAE,UAAU,kCAClD,OAAO,KACV,gBAAgB,EAAE,EAAE,eAAe,EAAE,IACrC,CAAC;QACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED,0IAA0I;IAC1I,iBAAiB,CACf,OAAe,EACf,UAAkB,EAClB,UAA2C,EAAE,cAAc,EAAE,EAAE,EAAE;QAEjE,OAAO,IAAA,iCAAiB,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACvE,CAAC;IAED,qFAAqF;IACrF,cAAc,CACZ,UAAiC,EACjC,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;QAE9D,OAAO,IAAA,8BAAc,EAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,8HAA8H;IAC9H,uBAAuB,CACrB,OAAe,EACf,iBAAoC,EACpC,UAAiD,EAAE,cAAc,EAAE,EAAE,EAAE;QAEvE,OAAO,IAAA,uCAAuB,EAC5B,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,iBAAiB,EACjB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,iKAAiK;IACjK,oBAAoB,CAClB,OAAe,EACf,UAA8C,EAAE,cAAc,EAAE,EAAE,EAAE;QAEpE,OAAO,IAAA,oCAAoB,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED,+WAA+W;IAC/W,iBAAiB,CACf,OAAe,EACf,UAA2C,EAAE,cAAc,EAAE,EAAE,EAAE;QAEjE,OAAO,IAAA,iCAAiB,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,+PAA+P;IAC/P,eAAe,CACb,OAAe,EACf,UAAyC,EAAE,cAAc,EAAE,EAAE,EAAE;QAE/D,OAAO,IAAA,+BAAe,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAED,2PAA2P;IAC3P,aAAa,CACX,OAAe,EACf,UAAuC,EAAE,cAAc,EAAE,EAAE,EAAE;QAE7D,OAAO,IAAA,6BAAa,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,gbAAgb;IAChb,cAAc,CACZ,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;QAE9D,OAAO,IAAA,8BAAc,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,+JAA+J;IAC/J,OAAO,CACL,OAAe,EACf,UAAkB,EAClB,UAAgC,EAChC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEvD,OAAO,IAAA,uBAAO,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,yVAAyV;IACzV,SAAS,CACP,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED,0hBAA0hB;IAC1hB,OAAO,CACL,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEvD,OAAO,IAAA,uBAAO,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,8aAA8a;IAC9a,MAAM,CACJ,OAAe,EACf,UAAkB,EAClB,UAA+B,EAC/B,UAAgC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEtD,OAAO,IAAA,sBAAM,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IAED,8MAA8M;IAC9M,IAAI,CACF,OAAe,EACf,UAAkB,EAClB,UAA6B,EAC7B,UAA8B,EAAE,cAAc,EAAE,EAAE,EAAE;QAEpD,OAAO,IAAA,oBAAI,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED,+uBAA+uB;IAC/uB,OAAO,CACL,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEvD,OAAO,IAAA,uBAAO,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,sqBAAsqB;IACtqB,OAAO,CACL,OAAe,EACf,UAAkB,EAClB,UAAmC,EACnC,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEvD,OAAO,IAAA,uBAAO,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,45BAA45B;IAC55B,UAAU,CACR,UAAgC,EAChC,UAAoC,EAAE,cAAc,EAAE,EAAE,EAAE;QAE1D,OAAO,IAAA,0BAAU,EAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,o7BAAo7B;IACp7B,SAAS,CACP,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,wXAAwX;IACxX,OAAO,CACL,UAAiC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEvD,OAAO,IAAA,uBAAO,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,oIAAoI;IACpI,cAAc,CACZ,OAAe,EACf,UAAwC,EAAE,cAAc,EAAE,EAAE,EAAE;QAE9D,OAAO,IAAA,8BAAc,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,kMAAkM;IAClM,MAAM,CACJ,OAAe,EACf,UAAkB,EAClB,UAAgC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEtD,OAAO,IAAA,sBAAM,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED,+MAA+M;IAC/M,SAAS,CACP,OAAe,EACf,UAAkB,EAClB,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;IAED,mTAAmT;IACnT,SAAS,CACP,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,kOAAkO;IAClO,SAAS,CACP,OAAe,EACf,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;IAED,yGAAyG;IACzG,SAAS,CACP,OAAe,EACf,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,iNAAiN;IACjN,SAAS,CACP,OAAe,EACf,UAA+B,EAC/B,UAAmC,EAAE,cAAc,EAAE,EAAE,EAAE;QAEzD,OAAO,IAAA,yBAAS,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;CACF;AArPD,wCAqPC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport {\n  create<PERSON><PERSON>Vault,\n  KeyVaultContext,\n  KeyVaultClientOptionalParams,\n} from \"./api/index.js\";\nimport {\n  KeyCreateParameters,\n  KeyBundle,\n  KeyImportParameters,\n  DeletedKeyBundle,\n  KeyUpdateParameters,\n  KeyItem,\n  BackupKeyResult,\n  KeyRestoreParameters,\n  KeyOperationsParameters,\n  KeyOperationResult,\n  KeySignParameters,\n  KeyVerifyParameters,\n  KeyVerifyResult,\n  KeyReleaseParameters,\n  KeyReleaseResult,\n  DeletedKeyItem,\n  KeyRotationPolicy,\n  GetRandomBytesRequest,\n  RandomBytes,\n} from \"./models/models.js\";\nimport {\n  GetKeyAttestationOptionalParams,\n  GetRandomBytesOptionalParams,\n  UpdateKeyRotationPolicyOptionalParams,\n  GetKeyRotationPolicyOptionalParams,\n  RecoverDeletedKeyOptionalParams,\n  PurgeDeletedKeyOptionalParams,\n  GetDeletedKeyOptionalParams,\n  GetDeletedKeysOptionalParams,\n  ReleaseOptionalParams,\n  UnwrapKeyOptionalParams,\n  WrapKeyOptionalParams,\n  VerifyOptionalParams,\n  SignOptionalParams,\n  DecryptOptionalParams,\n  EncryptOptionalParams,\n  RestoreKeyOptionalParams,\n  BackupKeyOptionalParams,\n  GetKeysOptionalParams,\n  GetKeyVersionsOptionalParams,\n  GetKeyOptionalParams,\n  UpdateKeyOptionalParams,\n  DeleteKeyOptionalParams,\n  ImportKeyOptionalParams,\n  RotateKeyOptionalParams,\n  CreateKeyOptionalParams,\n} from \"./api/options.js\";\nimport {\n  getKeyAttestation,\n  getRandomBytes,\n  updateKeyRotationPolicy,\n  getKeyRotationPolicy,\n  recoverDeletedKey,\n  purgeDeletedKey,\n  getDeletedKey,\n  getDeletedKeys,\n  release,\n  unwrapKey,\n  wrapKey,\n  verify,\n  sign,\n  decrypt,\n  encrypt,\n  restoreKey,\n  backupKey,\n  getKeys,\n  getKeyVersions,\n  getKey,\n  updateKey,\n  deleteKey,\n  importKey,\n  rotateKey,\n  createKey,\n} from \"./api/operations.js\";\nimport { PagedAsyncIterableIterator } from \"./static-helpers/pagingHelpers.js\";\nimport { Pipeline } from \"@azure/core-rest-pipeline\";\nimport { TokenCredential } from \"@azure/core-auth\";\n\nexport { KeyVaultClientOptionalParams } from \"./api/keyVaultContext.js\";\n\nexport class KeyVaultClient {\n  private _client: KeyVaultContext;\n  /** The pipeline used by this client to make requests */\n  public readonly pipeline: Pipeline;\n\n  /** The key vault client performs cryptographic key operations and vault operations against the Key Vault service. */\n  constructor(\n    endpointParam: string,\n    credential: TokenCredential,\n    options: KeyVaultClientOptionalParams = {},\n  ) {\n    const prefixFromOptions = options?.userAgentOptions?.userAgentPrefix;\n    const userAgentPrefix = prefixFromOptions\n      ? `${prefixFromOptions} azsdk-js-client`\n      : `azsdk-js-client`;\n    this._client = createKeyVault(endpointParam, credential, {\n      ...options,\n      userAgentOptions: { userAgentPrefix },\n    });\n    this.pipeline = this._client.pipeline;\n  }\n\n  /** The get key attestation operation returns the key along with its attestation blob. This operation requires the keys/get permission. */\n  getKeyAttestation(\n    keyName: string,\n    keyVersion: string,\n    options: GetKeyAttestationOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return getKeyAttestation(this._client, keyName, keyVersion, options);\n  }\n\n  /** Get the requested number of bytes containing random values from a managed HSM. */\n  getRandomBytes(\n    parameters: GetRandomBytesRequest,\n    options: GetRandomBytesOptionalParams = { requestOptions: {} },\n  ): Promise<RandomBytes> {\n    return getRandomBytes(this._client, parameters, options);\n  }\n\n  /** Set specified members in the key policy. Leave others as undefined. This operation requires the keys/update permission. */\n  updateKeyRotationPolicy(\n    keyName: string,\n    keyRotationPolicy: KeyRotationPolicy,\n    options: UpdateKeyRotationPolicyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyRotationPolicy> {\n    return updateKeyRotationPolicy(\n      this._client,\n      keyName,\n      keyRotationPolicy,\n      options,\n    );\n  }\n\n  /** The GetKeyRotationPolicy operation returns the specified key policy resources in the specified key vault. This operation requires the keys/get permission. */\n  getKeyRotationPolicy(\n    keyName: string,\n    options: GetKeyRotationPolicyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyRotationPolicy> {\n    return getKeyRotationPolicy(this._client, keyName, options);\n  }\n\n  /** The Recover Deleted Key operation is applicable for deleted keys in soft-delete enabled vaults. It recovers the deleted key back to its latest version under /keys. An attempt to recover an non-deleted key will return an error. Consider this the inverse of the delete operation on soft-delete enabled vaults. This operation requires the keys/recover permission. */\n  recoverDeletedKey(\n    keyName: string,\n    options: RecoverDeletedKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return recoverDeletedKey(this._client, keyName, options);\n  }\n\n  /** The Purge Deleted Key operation is applicable for soft-delete enabled vaults. While the operation can be invoked on any vault, it will return an error if invoked on a non soft-delete enabled vault. This operation requires the keys/purge permission. */\n  purgeDeletedKey(\n    keyName: string,\n    options: PurgeDeletedKeyOptionalParams = { requestOptions: {} },\n  ): Promise<void> {\n    return purgeDeletedKey(this._client, keyName, options);\n  }\n\n  /** The Get Deleted Key operation is applicable for soft-delete enabled vaults. While the operation can be invoked on any vault, it will return an error if invoked on a non soft-delete enabled vault. This operation requires the keys/get permission. */\n  getDeletedKey(\n    keyName: string,\n    options: GetDeletedKeyOptionalParams = { requestOptions: {} },\n  ): Promise<DeletedKeyBundle> {\n    return getDeletedKey(this._client, keyName, options);\n  }\n\n  /** Retrieves a list of the keys in the Key Vault as JSON Web Key structures that contain the public part of a deleted key. This operation includes deletion-specific information. The Get Deleted Keys operation is applicable for vaults enabled for soft-delete. While the operation can be invoked on any vault, it will return an error if invoked on a non soft-delete enabled vault. This operation requires the keys/list permission. */\n  getDeletedKeys(\n    options: GetDeletedKeysOptionalParams = { requestOptions: {} },\n  ): PagedAsyncIterableIterator<DeletedKeyItem> {\n    return getDeletedKeys(this._client, options);\n  }\n\n  /** The release key operation is applicable to all key types. The target key must be marked exportable. This operation requires the keys/release permission. */\n  release(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyReleaseParameters,\n    options: ReleaseOptionalParams = { requestOptions: {} },\n  ): Promise<KeyReleaseResult> {\n    return release(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The UNWRAP operation supports decryption of a symmetric key using the target key encryption key. This operation is the reverse of the WRAP operation. The UNWRAP operation applies to asymmetric and symmetric keys stored in Azure Key Vault since it uses the private portion of the key. This operation requires the keys/unwrapKey permission. */\n  unwrapKey(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyOperationsParameters,\n    options: UnwrapKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyOperationResult> {\n    return unwrapKey(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The WRAP operation supports encryption of a symmetric key using a key encryption key that has previously been stored in an Azure Key Vault. The WRAP operation is only strictly necessary for symmetric keys stored in Azure Key Vault since protection with an asymmetric key can be performed using the public portion of the key. This operation is supported for asymmetric keys as a convenience for callers that have a key-reference but do not have access to the public key material. This operation requires the keys/wrapKey permission. */\n  wrapKey(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyOperationsParameters,\n    options: WrapKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyOperationResult> {\n    return wrapKey(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The VERIFY operation is applicable to symmetric keys stored in Azure Key Vault. VERIFY is not strictly necessary for asymmetric keys stored in Azure Key Vault since signature verification can be performed using the public portion of the key but this operation is supported as a convenience for callers that only have a key-reference and not the public portion of the key. This operation requires the keys/verify permission. */\n  verify(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyVerifyParameters,\n    options: VerifyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyVerifyResult> {\n    return verify(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The SIGN operation is applicable to asymmetric and symmetric keys stored in Azure Key Vault since this operation uses the private portion of the key. This operation requires the keys/sign permission. */\n  sign(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeySignParameters,\n    options: SignOptionalParams = { requestOptions: {} },\n  ): Promise<KeyOperationResult> {\n    return sign(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The DECRYPT operation decrypts a well-formed block of ciphertext using the target encryption key and specified algorithm. This operation is the reverse of the ENCRYPT operation; only a single block of data may be decrypted, the size of this block is dependent on the target key and the algorithm to be used. The DECRYPT operation applies to asymmetric and symmetric keys stored in Azure Key Vault since it uses the private portion of the key. This operation requires the keys/decrypt permission. Microsoft recommends not to use CBC algorithms for decryption without first ensuring the integrity of the ciphertext using an HMAC, for example. See https://learn.microsoft.com/dotnet/standard/security/vulnerabilities-cbc-mode for more information. */\n  decrypt(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyOperationsParameters,\n    options: DecryptOptionalParams = { requestOptions: {} },\n  ): Promise<KeyOperationResult> {\n    return decrypt(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The ENCRYPT operation encrypts an arbitrary sequence of bytes using an encryption key that is stored in Azure Key Vault. Note that the ENCRYPT operation only supports a single block of data, the size of which is dependent on the target key and the encryption algorithm to be used. The ENCRYPT operation is only strictly necessary for symmetric keys stored in Azure Key Vault since protection with an asymmetric key can be performed using public portion of the key. This operation is supported for asymmetric keys as a convenience for callers that have a key-reference but do not have access to the public key material. This operation requires the keys/encrypt permission. */\n  encrypt(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyOperationsParameters,\n    options: EncryptOptionalParams = { requestOptions: {} },\n  ): Promise<KeyOperationResult> {\n    return encrypt(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** Imports a previously backed up key into Azure Key Vault, restoring the key, its key identifier, attributes and access control policies. The RESTORE operation may be used to import a previously backed up key. Individual versions of a key cannot be restored. The key is restored in its entirety with the same key name as it had when it was backed up. If the key name is not available in the target Key Vault, the RESTORE operation will be rejected. While the key name is retained during restore, the final key identifier will change if the key is restored to a different vault. Restore will restore all versions and preserve version identifiers. The RESTORE operation is subject to security constraints: The target Key Vault must be owned by the same Microsoft Azure Subscription as the source Key Vault The user must have RESTORE permission in the target Key Vault. This operation requires the keys/restore permission. */\n  restoreKey(\n    parameters: KeyRestoreParameters,\n    options: RestoreKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return restoreKey(this._client, parameters, options);\n  }\n\n  /** The Key Backup operation exports a key from Azure Key Vault in a protected form. Note that this operation does NOT return key material in a form that can be used outside the Azure Key Vault system, the returned key material is either protected to a Azure Key Vault HSM or to Azure Key Vault itself. The intent of this operation is to allow a client to GENERATE a key in one Azure Key Vault instance, BACKUP the key, and then RESTORE it into another Azure Key Vault instance. The BACKUP operation may be used to export, in protected form, any key type from Azure Key Vault. Individual versions of a key cannot be backed up. BACKUP / RESTORE can be performed within geographical boundaries only; meaning that a BACKUP from one geographical area cannot be restored to another geographical area. For example, a backup from the US geographical area cannot be restored in an EU geographical area. This operation requires the key/backup permission. */\n  backupKey(\n    keyName: string,\n    options: BackupKeyOptionalParams = { requestOptions: {} },\n  ): Promise<BackupKeyResult> {\n    return backupKey(this._client, keyName, options);\n  }\n\n  /** Retrieves a list of the keys in the Key Vault as JSON Web Key structures that contain the public part of a stored key. The LIST operation is applicable to all key types, however only the base key identifier, attributes, and tags are provided in the response. Individual versions of a key are not listed in the response. This operation requires the keys/list permission. */\n  getKeys(\n    options: GetKeysOptionalParams = { requestOptions: {} },\n  ): PagedAsyncIterableIterator<KeyItem> {\n    return getKeys(this._client, options);\n  }\n\n  /** The full key identifier, attributes, and tags are provided in the response. This operation requires the keys/list permission. */\n  getKeyVersions(\n    keyName: string,\n    options: GetKeyVersionsOptionalParams = { requestOptions: {} },\n  ): PagedAsyncIterableIterator<KeyItem> {\n    return getKeyVersions(this._client, keyName, options);\n  }\n\n  /** The get key operation is applicable to all key types. If the requested key is symmetric, then no key material is released in the response. This operation requires the keys/get permission. */\n  getKey(\n    keyName: string,\n    keyVersion: string,\n    options: GetKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return getKey(this._client, keyName, keyVersion, options);\n  }\n\n  /** In order to perform this operation, the key must already exist in the Key Vault. Note: The cryptographic material of a key itself cannot be changed. This operation requires the keys/update permission. */\n  updateKey(\n    keyName: string,\n    keyVersion: string,\n    parameters: KeyUpdateParameters,\n    options: UpdateKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return updateKey(this._client, keyName, keyVersion, parameters, options);\n  }\n\n  /** The delete key operation cannot be used to remove individual versions of a key. This operation removes the cryptographic material associated with the key, which means the key is not usable for Sign/Verify, Wrap/Unwrap or Encrypt/Decrypt operations. This operation requires the keys/delete permission. */\n  deleteKey(\n    keyName: string,\n    options: DeleteKeyOptionalParams = { requestOptions: {} },\n  ): Promise<DeletedKeyBundle> {\n    return deleteKey(this._client, keyName, options);\n  }\n\n  /** The import key operation may be used to import any key type into an Azure Key Vault. If the named key already exists, Azure Key Vault creates a new version of the key. This operation requires the keys/import permission. */\n  importKey(\n    keyName: string,\n    parameters: KeyImportParameters,\n    options: ImportKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return importKey(this._client, keyName, parameters, options);\n  }\n\n  /** The operation will rotate the key based on the key policy. It requires the keys/rotate permission. */\n  rotateKey(\n    keyName: string,\n    options: RotateKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return rotateKey(this._client, keyName, options);\n  }\n\n  /** The create key operation can be used to create any key type in Azure Key Vault. If the named key already exists, Azure Key Vault creates a new version of the key. It requires the keys/create permission. */\n  createKey(\n    keyName: string,\n    parameters: KeyCreateParameters,\n    options: CreateKeyOptionalParams = { requestOptions: {} },\n  ): Promise<KeyBundle> {\n    return createKey(this._client, keyName, parameters, options);\n  }\n}\n"]}