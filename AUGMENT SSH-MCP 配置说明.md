# AUGMENT SSH-MCP 配置说明

## 概述
本文档说明如何在 AUGMENT 中配置 SSH MCP 服务器，实现通过自然语言控制远程服务器进行开发操作。

## AUGMENT MCP 配置格式

### 方法一：使用 AUGMENT 设置面板（推荐）
1. 打开 AUGMENT 面板右上角的选项菜单
2. 点击 "Settings" 选项
3. 在 MCP 服务器部分填写配置
4. 点击 `+` 按钮添加新服务器

### 方法二：编辑 settings.json 文件
1. 按 `Cmd/Ctrl + Shift + P` 或点击 AUGMENT 面板的汉堡菜单
2. 选择 "Edit Settings"
3. 在 Advanced 下点击 "Edit in settings.json"

## SSH MCP 配置示例

### 完整配置格式
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "ssh-mcp",
        "command": "F:\\MCP\\SSH服务器管理\\venv\\nodejs\\node.exe",
        "args": [
          "F:\\MCP\\SSH服务器管理\\venv\\node_modules\\ssh-mcp\\build\\index.js",
          "--host=*************",
          "--port=22",
          "--user=ubuntu",
          "--password=ymjatTUU520",
          "--timeout=30000"
        ]
      }
    ]
  }
}
```

> **注意**：环境变量可以通过两种方式设置：
> 1. 在启动命令前设置：`"command": "NODE_PATH=path node"`
> 2. 在系统环境变量中预先设置

## 配置参数说明

### 基础参数
- **name**: 服务器唯一名称，必须唯一
- **command**: 执行命令的完整路径
- **args**: 命令行参数数组

### SSH 连接参数
- **--host**: 远程服务器 IP 地址
- **--port**: SSH 端口（默认 22）
- **--user**: SSH 用户名
- **--password**: SSH 密码
- **--timeout**: 命令执行超时时间（毫秒）

### 环境变量
- **NODE_PATH**: Node.js 模块搜索路径

## 安全注意事项

⚠️ **密码安全**
- 配置文件中包含明文密码
- 建议使用 SSH 密钥认证替代密码
- 确保配置文件权限设置正确

### SSH 密钥认证配置（推荐）
```json
{
  "name": "ssh-mcp-key",
  "command": "node",
  "args": [
    "path/to/ssh-mcp/build/index.js",
    "--host=*************",
    "--port=22",
    "--user=ubuntu",
    "--key=C:\\Users\\<USER>\\.ssh\\id_rsa",
    "--timeout=30000"
  ]
}
```

## 使用场景

### 网站开发常用操作
- 执行编译命令：`npm run build`
- 启动开发服务器：`npm run dev`
- 查看日志：`tail -f /var/log/nginx/error.log`
- 重启服务：`sudo systemctl restart nginx`
- Git 操作：`git pull origin main`

### 示例对话
```
用户：帮我在服务器上编译项目
AUGMENT：正在执行 npm run build...

用户：查看 nginx 错误日志的最后 20 行
AUGMENT：正在执行 tail -20 /var/log/nginx/error.log...
```

## 故障排除

### 常见问题
1. **连接失败**
   - 检查服务器 IP 和端口
   - 验证用户名和密码
   - 确认防火墙设置

2. **命令执行超时**
   - 增加 timeout 值
   - 检查命令是否需要交互输入

3. **Node.js 路径错误**
   - 验证 NODE_PATH 环境变量
   - 确认 node.exe 路径正确

### 调试方法
- 在终端手动测试 SSH 连接
- 检查 AUGMENT 输出面板的错误信息
- 验证 MCP 服务器是否正常启动

## 配置完成后
1. 重启 VS Code 编辑器
2. 打开 AUGMENT 面板
3. 验证 SSH MCP 服务器已加载
4. 开始使用自然语言控制远程服务器

---
**注意**：配置完成后，您可以直接通过自然语言与 AUGMENT 对话来操作远程服务器，无需记忆复杂的命令行语法。
