export { KeyCreateParameters, KnownJsonWebKeyType, JsonWebKeyType, KnownJsonWebKeyOperation, JsonWebKeyOperation, KeyAttributes, KnownDeletionRecoveryLevel, DeletionRecoveryLevel, KeyAttestation, KnownJsonWebKeyCurveName, JsonWebKeyCurveName, KeyReleasePolicy, KeyBundle, JsonWebKey, KeyVaultError, ErrorModel, KeyImportParameters, DeletedKeyBundle, KeyUpdateParameters, KeyItem, BackupKeyResult, KeyRestoreParameters, KeyOperationsParameters, KnownJsonWebKeyEncryptionAlgorithm, JsonWebKeyEncryptionAlgorithm, KeyOperationResult, KeySignParameters, KnownJsonWebKeySignatureAlgorithm, JsonWebKeySignatureAlgorithm, KeyVerifyParameters, KeyVerifyResult, KeyReleaseParameters, KnownKeyEncryptionAlgorithm, KeyEncryptionAlgorithm, KeyReleaseResult, DeletedKeyItem, KeyRotationPolicy, LifetimeActions, LifetimeActionsTrigger, LifetimeActionsType, KeyRotationPolicyAction, KeyRotationPolicyAttributes, GetRandomBytesRequest, RandomBytes, KnownVersions, } from "./models.js";
//# sourceMappingURL=index.d.ts.map