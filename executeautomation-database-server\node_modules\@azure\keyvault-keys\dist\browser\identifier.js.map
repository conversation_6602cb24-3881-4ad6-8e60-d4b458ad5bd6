{"version": 3, "file": "identifier.js", "sourceRoot": "", "sources": ["../../src/identifier.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AA8BjE;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,0BAA0B,CAAC,EAAU;IACnD,MAAM,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/B,uBACE,QAAQ,EAAE,EAAE,IACT,uBAAuB,CAAC,UAAU,EAAE,EAAE,CAAC,EAC1C;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { parseKeyVaultIdentifier } from \"@azure/keyvault-common\";\n\n/**\n * Represents the segments that compose a Key Vault Key Id.\n */\nexport interface KeyVaultKeyIdentifier {\n  /**\n   * The complete representation of the Key Vault Key Id. For example:\n   *\n   *   https://<keyvault-name>.vault.azure.net/keys/<key-name>/<unique-version-id>\n   *\n   */\n  sourceId: string;\n\n  /**\n   * The URL of the Azure Key Vault instance to which the Key belongs.\n   */\n  vaultUrl: string;\n\n  /**\n   * The version of Key Vault Key. Might be undefined.\n   */\n  version?: string;\n\n  /**\n   * The name of the Key Vault Key.\n   */\n  name: string;\n}\n\n/**\n * Parses the given Key Vault Key Id. An example is:\n *\n *   https://<keyvault-name>.vault.azure.net/keys/<key-name>/<unique-version-id>\n *\n * On parsing the above Id, this function returns:\n *```ts snippet:ignore\n *   {\n *      sourceId: \"https://<keyvault-name>.vault.azure.net/keys/<key-name>/<unique-version-id>\",\n *      vaultUrl: \"https://<keyvault-name>.vault.azure.net\",\n *      version: \"<unique-version-id>\",\n *      name: \"<key-name>\"\n *   }\n *```\n * @param id - The Id of the Key Vault Key.\n */\nexport function parseKeyVaultKeyIdentifier(id: string): KeyVaultKeyIdentifier {\n  const urlParts = id.split(\"/\");\n  const collection = urlParts[3];\n\n  return {\n    sourceId: id,\n    ...parseKeyVaultIdentifier(collection, id),\n  };\n}\n"]}